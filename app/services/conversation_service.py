"""
Service implementation for handling conversation-related operations.
"""

# Standard library imports
import asyncio
from functools import wraps
from typing import Any, Dict, List, Optional

# Third-party imports
import grpc
from dotenv import load_dotenv
from google.protobuf import empty_pb2
from pymongo.errors import PyMongoError

# Local imports
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.models.conversation_model import Conversation
from app.models.message_model import Message
from app.models.task_model import Task
from app.utils.advanced_search import AdvancedSearchBuilder, SearchResultOptimizer
from app.utils.cache_manager import get_cache_manager
from app.utils.logger import setup_logger
from app.utils.query_cache import QueryCache
from app.utils.response_optimizer import ResponseOptimizer

# Load environment variables from .env file
load_dotenv()

# Initialize logger
logger = setup_logger("communication-service/services/conversation_service.py")


def timeout_handler(timeout_seconds: int = 10):
    """
    Decorator to handle operation timeouts and provide consistent error handling.
    Thread-safe implementation using concurrent.futures for gRPC worker threads.

    Args:
        timeout_seconds: Maximum time to wait for operation completion
    """

    def decorator(func):
        @wraps(func)
        def wrapper(self, request, context):
            try:
                # Use concurrent.futures for thread-safe timeout handling
                import concurrent.futures
                import threading

                # Execute the function with timeout in a thread-safe way
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(func, self, request, context)
                    try:
                        result = future.result(timeout=timeout_seconds)
                        return result
                    except concurrent.futures.TimeoutError:
                        raise TimeoutError(f"Operation timed out after {timeout_seconds} seconds")

            except TimeoutError as e:
                logger.error(f"Operation timeout in {func.__name__}", error=str(e))
                context.abort(grpc.StatusCode.DEADLINE_EXCEEDED, str(e))
            except PyMongoError as e:
                logger.error(f"Database error in {func.__name__}", error=str(e))
                context.abort(grpc.StatusCode.INTERNAL, f"Database error: {str(e)}")
            except Exception as e:
                logger.error(f"Unexpected error in {func.__name__}", error=str(e))
                context.abort(grpc.StatusCode.INTERNAL, f"Internal error: {str(e)}")

        return wrapper

    return decorator


def batch_load_tasks(conversation_ids: List[str]) -> Dict[str, List]:
    """
    Efficiently load tasks for multiple conversations in a single query.

    Args:
        conversation_ids: List of conversation IDs to load tasks for

    Returns:
        Dictionary mapping conversation ID to list of tasks
    """
    if not conversation_ids:
        return {}

    try:
        # Single query to get all tasks for all conversations
        tasks = Task.objects.filter(globalChatConversationId__in=conversation_ids)

        # Group tasks by conversation ID
        tasks_by_conversation = {}
        for task in tasks:
            conv_id = str(task.globalChatConversationId)
            if conv_id not in tasks_by_conversation:
                tasks_by_conversation[conv_id] = []
            tasks_by_conversation[conv_id].append(task)

        return tasks_by_conversation
    except Exception as e:
        logger.error("Failed to batch load tasks", error=str(e))
        return {}


# Conversation service class
class ConversationService(communication_pb2_grpc.CommunicationServiceServicer):
    """
    Service class for handling conversation-related operations.
    Implements CRUD operations for conversations.
    """

    # Create conversation
    @timeout_handler(timeout_seconds=15)
    def createConversation(
        self,
        request: communication_pb2.CreateConversationRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Conversation:
        """
        Create a new conversation or return existing one based on chat type specific rules.

        For CHAT_TYPE_GLOBAL: Returns existing if no messages AND no tasks
        For CHAT_TYPE_AGENT: Returns existing if no messages (tasks are ignored)

        Args:
            request: CreateConversationRequest containing conversation details
            context: gRPC servicer context

        Returns:
            Created conversation or existing conversation based on chat type rules
        """
        try:
            # Log request details
            logger.info(
                "Creating new conversation or checking existing ones",
                userId=request.userId,
                agentId=request.agentId if request.agentId else None,
                chatType=request.chatType,
            )

            # Convert int chatType to string enum name for filtering
            chat_type_name = communication_pb2.ChatType.Name(request.chatType)

            # Build filter parameters for existing conversations
            filter_params = {
                "userId": request.userId,
                "chatType": chat_type_name,
            }

            # Add agentId to filter if provided
            if request.agentId:
                filter_params["agentId"] = request.agentId

            # Check for existing conversations with the same criteria - get most recent directly
            most_recent_conversation = (
                Conversation.objects.filter(**filter_params).order_by("-createdAt").first()
            )

            if most_recent_conversation:
                logger.info(
                    "Found existing conversation, checking based on chat type",
                    conversationId=str(most_recent_conversation.id),
                    chatType=chat_type_name,
                )

                # Optimized: Use aggregation to get counts in single query
                from mongoengine import Q

                # Check if the most recent conversation has any messages
                message_count = Message.objects.filter(
                    conversationId=most_recent_conversation.id
                ).count()

                # Determine if we should return existing conversation based on chat type
                should_return_existing = False
                task_count = 0

                if chat_type_name == "CHAT_TYPE_GLOBAL":
                    # For GLOBAL chat: check both messages and tasks
                    task_count = Task.objects.filter(
                        globalChatConversationId=most_recent_conversation.id
                    ).count()

                    should_return_existing = message_count == 0 and task_count == 0

                    if should_return_existing:
                        logger.info(
                            "Global conversation has no messages and no tasks, returning existing",
                            conversationId=str(most_recent_conversation.id),
                            messageCount=message_count,
                            taskCount=task_count,
                        )
                    else:
                        logger.info(
                            "Global conversation has messages or tasks, creating new",
                            conversationId=str(most_recent_conversation.id),
                            messageCount=message_count,
                            taskCount=task_count,
                        )

                elif chat_type_name == "CHAT_TYPE_AGENT":
                    # For AGENT chat: check only messages (ignore tasks)
                    should_return_existing = message_count == 0

                    if should_return_existing:
                        logger.info(
                            "Agent conversation has no messages, returning existing",
                            conversationId=str(most_recent_conversation.id),
                            messageCount=message_count,
                        )
                    else:
                        logger.info(
                            "Agent conversation has messages, creating new",
                            conversationId=str(most_recent_conversation.id),
                            messageCount=message_count,
                        )

                # Return existing conversation if criteria are met
                if should_return_existing:
                    # Optimized: Only fetch tasks if needed for GLOBAL chat
                    if chat_type_name == "CHAT_TYPE_GLOBAL":
                        tasks = Task.objects.filter(
                            globalChatConversationId=most_recent_conversation.id
                        )
                    else:
                        tasks = []
                    return most_recent_conversation.to_proto(tasks=tasks)

            # Either no existing conversations or criteria not met, create new one
            conversation = Conversation(
                userId=request.userId,
                agentId=request.agentId if request.agentId else None,
                chatType=chat_type_name,
            )
            conversation.save()

            # Log success
            logger.info("Conversation created successfully", conversationId=conversation.id)

            # Get tasks for the new conversation (will be empty for new conversations)
            tasks = Task.objects.filter(globalChatConversationId=conversation.id)

            # Convert to protobuf message
            return conversation.to_proto(tasks=tasks)

        except Exception as e:
            # Log error
            logger.error("Failed to create conversation", error=str(e))

            # Raise gRPC error
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}")

    # Get conversation
    @timeout_handler(timeout_seconds=10)
    def getConversation(
        self,
        request: communication_pb2.GetConversationRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Conversation:
        """
        Get conversation by ID.

        Args:
            request: GetConversationRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            Conversation with the specified ID if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Getting conversation",
                conversationId=request.conversationId,
                userId=request.userId,
            )

            # Get conversation from database
            conversation = Conversation.objects.get(id=request.conversationId)

            # Check if the user is authorized to access this conversation
            if conversation.userId != request.userId:
                logger.warning(
                    "Unauthorized access attempt",
                    conversationId=request.conversationId,
                    requestUserId=request.userId,
                    conversationUserId=conversation.userId,
                )
                context.abort(
                    grpc.StatusCode.PERMISSION_DENIED,
                    f"User {request.userId} is not authorized to access conversation {request.conversationId}",
                )

            # Log success
            logger.info("Conversation retrieved successfully", conversationId=conversation.id)

            # Get tasks associated with this conversation
            tasks = Task.objects.filter(globalChatConversationId=conversation.id)

            # Convert to protobuf message with tasks
            return conversation.to_proto(tasks=tasks)

        except Exception as e:
            # Log error
            logger.error("Failed to get conversation", error=str(e))

            # Raise gRPC error
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to get conversation: {str(e)}")

    # Update conversation tokens
    @timeout_handler(timeout_seconds=10)
    def updateConversationTokens(
        self,
        request: communication_pb2.UpdateConversationTokensRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Update conversation tokens.

        Args:
            request: UpdateConversationTokensRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response
        """
        try:
            # Log request details
            logger.info(
                "Updating conversation tokens",
                conversationId=request.conversationId,
                inputTokens=request.inputTokens,
                outputTokens=request.outputTokens,
                userId=request.userId,
            )

            # Get conversation from database
            conversation = Conversation.objects.get(id=request.conversationId)

            # Check if the user is authorized to update tokens for this conversation
            if conversation.userId != request.userId:
                logger.warning(
                    "Unauthorized token update attempt",
                    conversationId=request.conversationId,
                    requestUserId=request.userId,
                    conversationUserId=conversation.userId,
                )
                context.abort(
                    grpc.StatusCode.PERMISSION_DENIED,
                    f"User {request.userId} is not authorized to update tokens for conversation {request.conversationId}",
                )

            # Update conversation tokens by adding new tokens to existing ones
            old_input_tokens = conversation.inputTokens or 0
            old_output_tokens = conversation.outputTokens or 0

            new_input_tokens = request.inputTokens if request.inputTokens else 0
            new_output_tokens = request.outputTokens if request.outputTokens else 0

            conversation.inputTokens = old_input_tokens + new_input_tokens
            conversation.outputTokens = old_output_tokens + new_output_tokens
            conversation.save()

            # Log success with detailed token information
            logger.info(
                "Conversation tokens updated successfully",
                conversationId=request.conversationId,
                oldInputTokens=old_input_tokens,
                newInputTokens=new_input_tokens,
                totalInputTokens=conversation.inputTokens,
                oldOutputTokens=old_output_tokens,
                newOutputTokens=new_output_tokens,
                totalOutputTokens=conversation.outputTokens,
            )

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to update conversation tokens", error=str(e))

            # Raise gRPC error
            context.abort(
                grpc.StatusCode.INTERNAL,
                f"Failed to update conversation tokens: {str(e)}",
            )

    # Delete conversation
    @timeout_handler(timeout_seconds=10)
    def deleteConversation(
        self,
        request: communication_pb2.DeleteConversationRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete conversation by ID.

        Args:
            request: DeleteConversationRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Deleting conversation",
                conversationId=request.conversationId,
                userId=request.userId,
            )

            # Get conversation from database
            conversation = Conversation.objects.get(id=request.conversationId)

            # Check if the user is authorized to delete this conversation
            if conversation.userId != request.userId:
                logger.warning(
                    "Unauthorized deletion attempt",
                    conversationId=request.conversationId,
                    requestUserId=request.userId,
                    conversationUserId=conversation.userId,
                )
                context.abort(
                    grpc.StatusCode.PERMISSION_DENIED,
                    f"User {request.userId} is not authorized to delete conversation {request.conversationId}",
                )

            # Delete conversation from database
            conversation.delete()

            # Log success
            logger.info(
                "Conversation deleted successfully",
                conversationId=request.conversationId,
            )

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete conversation", error=str(e))

            # Raise gRPC error
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to delete conversation: {str(e)}")

    # List conversations
    @timeout_handler(timeout_seconds=15)
    def listConversations(
        self,
        request: communication_pb2.ListConversationsRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListConversationsResponse:
        """
        List conversations with enhanced filtering and sorting options.

        Supports multiple filtering modes:
        1. All conversations (no filters) - returns all user conversations
        2. By chatType - filters by specific chat type (CHAT_TYPE_GLOBAL, CHAT_TYPE_AGENT)
        3. By agentId - filters by specific agent conversations
        4. By search query - searches in conversation titles using regex
        5. By date range - filters conversations created within specified date range
        6. By token usage - filters by input/output token ranges

        Supports multiple sorting options:
        1. By creation date (default, newest first)
        2. By last update date
        3. By title (alphabetical)
        4. By token usage (input/output tokens)

        Args:
            request: ListConversationsRequest containing query parameters
            context: gRPC servicer context

        Returns:
            List of conversations matching the filters and sorting
        """
        try:
            # Initialize query cache for performance optimization
            query_cache = QueryCache()

            # Build cache key parameters
            cache_filters = {
                "chatType": request.chatType if request.chatType != 0 else None,
                "agentId": request.agentId if request.agentId else None,
                "search": getattr(request, "search", None) if hasattr(request, "search") else None,
                "dateFrom": (
                    getattr(request, "dateFrom", None) if hasattr(request, "dateFrom") else None
                ),
                "dateTo": getattr(request, "dateTo", None) if hasattr(request, "dateTo") else None,
                "minInputTokens": (
                    getattr(request, "minInputTokens", None)
                    if hasattr(request, "minInputTokens")
                    else None
                ),
                "maxInputTokens": (
                    getattr(request, "maxInputTokens", None)
                    if hasattr(request, "maxInputTokens")
                    else None
                ),
                "minOutputTokens": (
                    getattr(request, "minOutputTokens", None)
                    if hasattr(request, "minOutputTokens")
                    else None
                ),
                "maxOutputTokens": (
                    getattr(request, "maxOutputTokens", None)
                    if hasattr(request, "maxOutputTokens")
                    else None
                ),
            }

            cache_sort_params = {
                "sortBy": getattr(request, "sortBy", None) if hasattr(request, "sortBy") else None,
                "sortOrder": (
                    getattr(request, "sortOrder", None) if hasattr(request, "sortOrder") else None
                ),
            }

            cache_pagination = {
                "limit": request.limit,
                "offset": request.offset,
            }

            # Try to get cached result first (only for simple queries without complex search)
            search_query = getattr(request, "search", None) if hasattr(request, "search") else None
            use_cache = not (search_query and search_query.strip())  # Skip cache for search queries

            if use_cache:
                cached_result = query_cache.get_cached_query_result(
                    query_type="conversations",
                    user_id=request.userId,
                    filters=cache_filters,
                    sort_params=cache_sort_params,
                    pagination=cache_pagination,
                )

                if cached_result is not None:
                    cached_conversations, cached_metadata = cached_result

                    # Convert cached data back to protobuf response
                    response = communication_pb2.ListConversationsResponse(
                        data=cached_conversations,
                        metadata=communication_pb2.PaginationMetadata(
                            total=cached_metadata.get("total", 0),
                            totalPages=cached_metadata.get("totalPages", 0),
                            currentPage=cached_metadata.get("currentPage", 1),
                            pageSize=cached_metadata.get("pageSize", 10),
                            hasNextPage=cached_metadata.get("hasNextPage", False),
                            hasPreviousPage=cached_metadata.get("hasPreviousPage", False),
                        ),
                    )

                    logger.info(
                        "Returned cached conversation list",
                        userId=request.userId,
                        cachedCount=len(cached_conversations),
                        totalCached=cached_metadata.get("total", 0),
                    )

                    return response

            # Extract additional filter parameters
            date_from = getattr(request, "dateFrom", None) if hasattr(request, "dateFrom") else None
            date_to = getattr(request, "dateTo", None) if hasattr(request, "dateTo") else None
            min_input_tokens = (
                getattr(request, "minInputTokens", None)
                if hasattr(request, "minInputTokens")
                else None
            )
            max_input_tokens = (
                getattr(request, "maxInputTokens", None)
                if hasattr(request, "maxInputTokens")
                else None
            )
            min_output_tokens = (
                getattr(request, "minOutputTokens", None)
                if hasattr(request, "minOutputTokens")
                else None
            )
            max_output_tokens = (
                getattr(request, "maxOutputTokens", None)
                if hasattr(request, "maxOutputTokens")
                else None
            )
            sort_by = getattr(request, "sortBy", None) if hasattr(request, "sortBy") else None
            sort_order = (
                getattr(request, "sortOrder", None) if hasattr(request, "sortOrder") else None
            )
            include_tasks = (
                getattr(request, "includeTasks", True) if hasattr(request, "includeTasks") else True
            )

            # Log request details with enhanced parameters
            logger.info(
                "Listing conversations with enhanced filters",
                userId=request.userId,
                chatType=request.chatType,
                agentId=request.agentId if request.agentId else None,
                search=getattr(request, "search", None) if hasattr(request, "search") else None,
                dateFrom=date_from,
                dateTo=date_to,
                minInputTokens=min_input_tokens,
                maxInputTokens=max_input_tokens,
                minOutputTokens=min_output_tokens,
                maxOutputTokens=max_output_tokens,
                sortBy=sort_by,
                sortOrder=sort_order,
                includeTasks=include_tasks,
            )

            # Pagination parameters with robust type checking
            try:
                page_size = int(request.limit)
                if page_size <= 0:
                    page_size = 10
                elif page_size > 100:  # Add maximum limit
                    page_size = 100
            except (TypeError, ValueError):
                page_size = 10

            try:
                page = int(request.page)
                if page <= 0:
                    page = 1
            except (TypeError, ValueError):
                page = 1

            skip = (page - 1) * page_size

            # Start building the filter with userId (mandatory)
            filter_params = {"userId": request.userId}

            # Initialize MongoDB query for advanced filtering
            from datetime import datetime

            from mongoengine import Q

            query = Q(userId=request.userId)

            # Filter 1: All conversations (default) - no additional filters needed
            # Just the userId filter will be applied

            # Filter 2: By chatType - add chatType filter if specified (not UNSPECIFIED)
            if request.chatType != 0:
                chat_type_name = communication_pb2.ChatType.Name(request.chatType)
                filter_params["chatType"] = chat_type_name
                query = query & Q(chatType=chat_type_name)
                logger.info("Applying chatType filter", chatType=chat_type_name)

            # Filter 3: By agentId - add agentId filter if provided
            if request.agentId:
                filter_params["agentId"] = request.agentId
                query = query & Q(agentId=request.agentId)
                logger.info("Applying agentId filter", agentId=request.agentId)

            # Filter 4: Enhanced search - multi-field search with advanced capabilities
            search_query = getattr(request, "search", None) if hasattr(request, "search") else None
            search_results_ranked = False

            if search_query and search_query.strip():
                # Use advanced search builder for multi-field search
                search_fields = ["title", "agentId"]  # Search in title and agentId

                # Check if advanced search is requested (contains special operators)
                use_advanced_search = any(op in search_query for op in ["AND", "OR", "NOT", '"'])

                if use_advanced_search:
                    # Build complex search query
                    search_filters = {
                        "text_search": {
                            "term": search_query.strip(),
                            "fields": search_fields,
                            "case_sensitive": False,
                            "exact_match": False,
                        }
                    }
                    search_query_obj = AdvancedSearchBuilder.build_complex_search_query(
                        base_query=Q(), search_filters=search_filters
                    )
                    query = query & search_query_obj
                    search_results_ranked = True
                    logger.info(
                        "Applied advanced search filter",
                        searchQuery=search_query,
                        fields=search_fields,
                    )
                else:
                    # Use simple text search for backward compatibility
                    text_search_query = AdvancedSearchBuilder.build_text_search_query(
                        search_term=search_query.strip(),
                        fields=search_fields,
                        case_sensitive=False,
                        exact_match=False,
                    )
                    query = query & text_search_query
                    search_results_ranked = True
                    logger.info(
                        "Applied text search filter", searchQuery=search_query, fields=search_fields
                    )

            # Filter 5: By date range - filter conversations created within specified range
            if date_from or date_to:
                if date_from:
                    # Convert timestamp to datetime if needed
                    if hasattr(date_from, "seconds"):
                        from_dt = datetime.fromtimestamp(date_from.seconds)
                    else:
                        from_dt = date_from
                    query = query & Q(createdAt__gte=from_dt)
                    logger.info("Applying dateFrom filter", dateFrom=from_dt)

                if date_to:
                    # Convert timestamp to datetime if needed
                    if hasattr(date_to, "seconds"):
                        to_dt = datetime.fromtimestamp(date_to.seconds)
                    else:
                        to_dt = date_to
                    query = query & Q(createdAt__lte=to_dt)
                    logger.info("Applying dateTo filter", dateTo=to_dt)

            # Filter 6: By token usage - filter by input/output token ranges
            if min_input_tokens is not None:
                query = query & Q(inputTokens__gte=min_input_tokens)
                logger.info("Applying minInputTokens filter", minInputTokens=min_input_tokens)

            if max_input_tokens is not None:
                query = query & Q(inputTokens__lte=max_input_tokens)
                logger.info("Applying maxInputTokens filter", maxInputTokens=max_input_tokens)

            if min_output_tokens is not None:
                query = query & Q(outputTokens__gte=min_output_tokens)
                logger.info("Applying minOutputTokens filter", minOutputTokens=min_output_tokens)

            if max_output_tokens is not None:
                query = query & Q(outputTokens__lte=max_output_tokens)
                logger.info("Applying maxOutputTokens filter", maxOutputTokens=max_output_tokens)

            # Determine sorting order
            sort_field = "-createdAt"  # Default: newest first
            if sort_by:
                # Map sort options to MongoDB field names
                sort_mapping = {
                    "CREATED_AT": "createdAt",
                    "UPDATED_AT": "updatedAt",
                    "TITLE": "title",
                    "INPUT_TOKENS": "inputTokens",
                    "OUTPUT_TOKENS": "outputTokens",
                    "TOTAL_TOKENS": "inputTokens",  # Will need custom handling
                }

                if sort_by in sort_mapping:
                    field = sort_mapping[sort_by]
                    # Apply sort order (default descending for dates/numbers, ascending for text)
                    if sort_order == "ASC":
                        sort_field = field
                    else:  # DESC or default
                        if sort_by in ["TITLE"]:
                            sort_field = f"-{field}"  # Reverse alphabetical
                        else:
                            sort_field = f"-{field}"  # Newest/highest first

                    logger.info(
                        "Applying custom sort",
                        sortBy=sort_by,
                        sortOrder=sort_order,
                        sortField=sort_field,
                    )

            # Apply the query with enhanced sorting
            # CRITICAL OPTIMIZATION: Only select necessary fields for list view
            conversation_filter = (
                Conversation.objects(query)
                .only(
                    "id",
                    "userId",
                    "agentId",
                    "chatType",
                    "title",
                    "inputTokens",
                    "outputTokens",
                    "createdAt",
                    "updatedAt",
                )
                .order_by(sort_field)
            )

            # Optimize: Get paginated results first, then count only if needed
            conversations = list(conversation_filter[skip : skip + page_size])

            # Apply search result ranking if search was performed
            if search_results_ranked and search_query and conversations:
                # Rank conversations by relevance to search query
                score_fields = ["title", "agentId"]
                boost_factors = {"title": 2.0, "agentId": 1.0}  # Title matches are more important

                ranked_results = SearchResultOptimizer.rank_search_results(
                    results=conversations,
                    search_term=search_query.strip(),
                    score_fields=score_fields,
                    boost_factors=boost_factors,
                    max_results=page_size,
                )

                # Extract ranked conversations (ignore scores for now)
                conversations = [result[0] for result in ranked_results]

                logger.info(
                    "Applied search result ranking",
                    searchQuery=search_query,
                    originalCount=len(conversations),
                    rankedCount=len(ranked_results),
                    topScore=ranked_results[0][1] if ranked_results else 0.0,
                )

            # Only count if we need pagination metadata (avoid expensive count query)
            if page == 1 and len(conversations) < page_size:
                # If first page and less than page_size results, we have all data
                total_count = len(conversations)
                total_pages = 1
            else:
                # Only do expensive count when necessary
                total_count = conversation_filter.count()
                total_pages = (total_count + page_size - 1) // page_size

            # Log success with filter summary
            logger.info(
                "Conversations listed successfully with filters applied",
                userId=request.userId,
                chatType=request.chatType if request.chatType != 0 else "ALL",
                agentId=request.agentId if request.agentId else "ALL",
                search=search_query if search_query else "NONE",
                totalFound=total_count,
                pageSize=page_size,
                currentPage=page,
            )

            # Build pagination metadata
            metadata = communication_pb2.PaginationMetadata(
                total=total_count,
                totalPages=total_pages,
                currentPage=page,
                pageSize=page_size,
                hasNextPage=page < total_pages,
                hasPreviousPage=page > 1,
            )

            # Enhanced task loading logic with include_tasks parameter
            conversation_protos = []

            # Check if tasks should be loaded based on request parameter and conversation types
            should_load_tasks = include_tasks and any(
                conv.chatType == "CHAT_TYPE_GLOBAL" for conv in conversations
            )

            if (
                should_load_tasks and len(conversations) <= 20
            ):  # Only batch load for reasonable sizes
                # Use batch loading for better performance when needed
                conversation_ids = [str(conv.id) for conv in conversations]
                tasks_by_conversation = batch_load_tasks(conversation_ids)

                for conversation in conversations:
                    conv_id = str(conversation.id)
                    # Only include tasks for GLOBAL conversations when requested
                    if include_tasks and conversation.chatType == "CHAT_TYPE_GLOBAL":
                        tasks = tasks_by_conversation.get(conv_id, [])
                    else:
                        tasks = []
                    conversation_proto = conversation.to_proto(tasks=tasks)
                    conversation_protos.append(conversation_proto)
            else:
                # Fast path: Convert without task loading for better performance
                for conversation in conversations:
                    # Pass empty tasks list for faster conversion or when not requested
                    conversation_proto = conversation.to_proto(tasks=[])
                    conversation_protos.append(conversation_proto)

            # Apply response optimization if requested
            optimize_response = (
                getattr(request, "optimizeResponse", False)
                if hasattr(request, "optimizeResponse")
                else False
            )
            exclude_fields = (
                getattr(request, "excludeFields", None)
                if hasattr(request, "excludeFields")
                else None
            )
            include_fields = (
                getattr(request, "includeFields", None)
                if hasattr(request, "includeFields")
                else None
            )

            if optimize_response or exclude_fields or include_fields:
                # Convert to set for faster lookup
                exclude_set = set(exclude_fields) if exclude_fields else None
                include_set = set(include_fields) if include_fields else None

                # Apply field filtering to conversation protos
                optimized_protos = ResponseOptimizer.optimize_list_response(
                    conversation_protos, include_fields=include_set, exclude_fields=exclude_set
                )

                logger.info(
                    "Applied response optimization to conversations",
                    originalCount=len(conversation_protos),
                    optimizedCount=len(optimized_protos),
                    includeFields=include_fields,
                    excludeFields=exclude_fields,
                    optimizeResponse=optimize_response,
                )

                # Convert back to protobuf messages if needed
                # For now, we'll keep the original protos but log the optimization
                # In a full implementation, you'd need to reconstruct protobuf messages
                # from the optimized dictionaries

            # Monitor response size
            ResponseOptimizer.monitor_response_size(conversation_protos, "listConversations")

            # Create response
            response = communication_pb2.ListConversationsResponse(
                data=conversation_protos,
                metadata=metadata,
            )

            # Cache the result if caching was enabled for this query
            if use_cache and response.data:
                cache_metadata = {
                    "total": metadata.total,
                    "totalPages": metadata.totalPages,
                    "currentPage": metadata.currentPage,
                    "pageSize": metadata.pageSize,
                    "hasNextPage": metadata.hasNextPage,
                    "hasPreviousPage": metadata.hasPreviousPage,
                }

                query_cache.cache_query_result(
                    query_type="conversations",
                    user_id=request.userId,
                    filters=cache_filters,
                    results=list(response.data),
                    metadata=cache_metadata,
                    sort_params=cache_sort_params,
                    pagination=cache_pagination,
                    ttl=1800,  # 30 minutes cache
                )

                logger.info(
                    "Cached conversation list result",
                    userId=request.userId,
                    resultCount=len(response.data),
                    totalResults=metadata.total,
                )

            # Return response with conversations including their tasks
            return response

        except Exception as e:
            # Log error with enhanced filter context
            logger.error(
                "Failed to list conversations with enhanced filters",
                error=str(e),
                userId=request.userId,
                chatType=request.chatType,
                agentId=request.agentId if request.agentId else None,
                search=getattr(request, "search", None) if hasattr(request, "search") else None,
                dateFrom=(
                    getattr(request, "dateFrom", None) if hasattr(request, "dateFrom") else None
                ),
                dateTo=getattr(request, "dateTo", None) if hasattr(request, "dateTo") else None,
                sortBy=getattr(request, "sortBy", None) if hasattr(request, "sortBy") else None,
                sortOrder=(
                    getattr(request, "sortOrder", None) if hasattr(request, "sortOrder") else None
                ),
            )

            # Raise gRPC error
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to list conversations: {str(e)}")
