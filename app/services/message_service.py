"""
Service implementation for handling message-related operations.
"""

# Standard libraries
import grpc
from google.protobuf import any_pb2, empty_pb2

# Local imports
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.models.conversation_model import Conversation
from app.models.message_model import Message
from app.utils.advanced_search import AdvancedSearchBuilder, SearchResultOptimizer
from app.utils.logger import setup_logger
from app.utils.response_optimizer import ResponseOptimizer

# Initialize logger
logger = setup_logger("communication-service/services/message_service.py")


# Message service class
class MessageService(communication_pb2_grpc.CommunicationServiceServicer):
    """
    Service class for handling message-related operations.
    Implements CRUD operations for messages with JSON data support.
    """

    # Create message
    def createMessage(
        self,
        request: communication_pb2.CreateMessageRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Message:
        """
        Create a new message with JSON data support.

        Args:
            request: CreateMessageRequest containing message details and user ID
            context: gRPC servicer context

        Returns:
            Created message if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Creating new message",
                conversationId=request.conversationId,
                senderType=request.senderType,
                hasData=bool(request.HasField("data")),
                workflowId=request.workflowId,
                workflowResponse=len(request.workflowResponse),
                status=request.status if request.HasField("status") else None,
                type=request.type,
            )

            # Check if the user is authorized to create a message in this conversation
            try:
                conversation = Conversation.objects.get(id=request.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message creation attempt",
                        conversationId=request.conversationId,
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to create messages in conversation {request.conversationId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=request.conversationId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {request.conversationId} not found",
                )

            # Convert int enums to string enum names
            sender_type_name = communication_pb2.SenderType.Name(request.senderType)
            message_type_name = communication_pb2.MessageType.Name(request.type)
            message_status_name = None
            if request.HasField("status"):
                message_status_name = communication_pb2.MessageStatus.Name(request.status)

            # Convert protobuf Struct data to dictionary for storage
            message_data = None
            if request.HasField("data"):
                try:
                    # Use MessageToDict for proper conversion of all nested structures
                    from google.protobuf.json_format import MessageToDict

                    message_data = MessageToDict(request.data)
                except Exception as e:
                    logger.error("Failed to convert protobuf Struct to dictionary", error=str(e))
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, f"Invalid data format: {str(e)}"
                    )

            # Populate the workflowResponse array field
            workflow_response_list = []
            if request.workflowResponse:
                from google.protobuf.json_format import MessageToDict

                for idx, struct_value in enumerate(request.workflowResponse):
                    try:
                        # Convert each Struct to dict
                        workflow_data = MessageToDict(struct_value)
                        workflow_response_list.append(workflow_data)
                    except Exception as e:
                        logger.error(
                            "Failed to process workflowResponse item",
                            index=idx,
                            error=str(e),
                        )

            # Create message in database
            message = Message(
                conversationId=request.conversationId,
                senderType=sender_type_name,
                data=message_data,
                workflowId=request.workflowId,
                workflowResponse=workflow_response_list,
                status=message_status_name,
                type=message_type_name,
            )
            message.save()

            # Update conversation title if not already set
            try:
                if not conversation.title and message_data:
                    # Extract title from message data
                    title = self._extract_title_from_message_data(message_data)
                    if title:
                        conversation.title = title
                        conversation.save()
                        logger.info(
                            "Updated conversation title",
                            conversationId=str(conversation.id),
                            title=title,
                        )
            except Exception as e:
                logger.warning("Failed to update conversation title", error=str(e))

            # Log success
            logger.info("Message created successfully", messageId=message.id)

            # Convert to protobuf message
            return message.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to create message", error=str(e))

            # Raise gRPC exception
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to create message: {str(e)}")

    # Delete message
    def deleteMessage(
        self,
        request: communication_pb2.DeleteMessageRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete a message.

        Args:
            request: DeleteMessageRequest containing message ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response if the user is authorized
        """
        try:
            # Log request details
            logger.info("Deleting message", messageId=request.messageId, userId=request.userId)

            # Get the message
            try:
                message = Message.objects.get(id=request.messageId)
            except Message.DoesNotExist:
                logger.error("Message not found", messageId=request.messageId)
                context.abort(grpc.StatusCode.NOT_FOUND, f"Message {request.messageId} not found")

            # Check if the user is authorized to delete this message
            try:
                conversation = Conversation.objects.get(id=message.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message deletion attempt",
                        messageId=request.messageId,
                        conversationId=str(message.conversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to delete message {request.messageId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=str(message.conversationId))
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {message.conversationId} not found",
                )

            # Delete message from database
            message.delete()

            # Log success
            logger.info("Message deleted successfully", messageId=request.messageId)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete message", error=str(e))

            # Raise gRPC exception
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to delete message: {str(e)}")

    # List messages
    def listMessages(
        self,
        request: communication_pb2.ListMessagesRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListMessagesResponse:
        """
        List messages for a conversation with enhanced filtering, sorting, and optimization features.

        Supports multiple filtering modes:
        1. By message type - filters by specific message types (chat, mcp, workflow, user_message)
        2. By sender type - filters by sender (user, agent)
        3. By status - filters by message status (running, completed, failed)
        4. By date range - filters messages created within specified date range
        5. By workflow ID - filters messages associated with specific workflows

        Supports multiple sorting options:
        1. By creation date (default, newest first)
        2. By update date
        3. By message type
        4. By sender type

        Performance optimizations:
        - Field selection for reduced response size
        - Optimized pagination with smart counting
        - Index-based retrieval for specific use cases
        - Conditional workflow response loading

        Args:
            request: ListMessagesRequest containing conversation ID, user ID, pagination parameters,
                    filtering options, sorting preferences, and optional index for custom range retrieval
            context: gRPC servicer context

        Returns:
            List of messages with JSON data if the user is authorized

        Index-based retrieval logic:
            - If index is provided and within normal page boundaries (e.g., page 2, limit 10 = 11-20),
              messages are returned from index to the end of the page boundary
            - If index is outside the page boundaries, normal pagination is used
            - Examples:
              * page=2, limit=10, index=15: returns messages 15-20 (if within boundary 11-20)
              * page=2, limit=10, index=5: returns messages 11-20 (index outside boundary)
        """
        try:
            # Extract enhanced filter parameters
            index_value = None
            if request.HasField("index"):
                index_value = request.index

            message_type = (
                getattr(request, "messageType", None) if hasattr(request, "messageType") else None
            )
            sender_type = (
                getattr(request, "senderType", None) if hasattr(request, "senderType") else None
            )
            status = getattr(request, "status", None) if hasattr(request, "status") else None
            workflow_id = (
                getattr(request, "workflowId", None) if hasattr(request, "workflowId") else None
            )
            date_from = getattr(request, "dateFrom", None) if hasattr(request, "dateFrom") else None
            date_to = getattr(request, "dateTo", None) if hasattr(request, "dateTo") else None
            sort_by = getattr(request, "sortBy", None) if hasattr(request, "sortBy") else None
            sort_order = (
                getattr(request, "sortOrder", None) if hasattr(request, "sortOrder") else None
            )
            include_workflow_response = (
                getattr(request, "includeWorkflowResponse", True)
                if hasattr(request, "includeWorkflowResponse")
                else True
            )
            fields_only = (
                getattr(request, "fieldsOnly", None) if hasattr(request, "fieldsOnly") else None
            )

            # Log request details with enhanced parameters
            logger.info(
                "Listing messages with enhanced filters",
                conversationId=request.conversationId,
                userId=request.userId,
                page=request.page,
                limit=request.limit,
                index=index_value,
                messageType=message_type,
                senderType=sender_type,
                status=status,
                workflowId=workflow_id,
                dateFrom=date_from,
                dateTo=date_to,
                sortBy=sort_by,
                sortOrder=sort_order,
                includeWorkflowResponse=include_workflow_response,
                fieldsOnly=fields_only,
            )

            # Check if the user is authorized to list messages in this conversation
            try:
                conversation = Conversation.objects.get(id=request.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message listing attempt",
                        conversationId=request.conversationId,
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to list messages in conversation {request.conversationId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=request.conversationId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {request.conversationId} not found",
                )

            # Pagination parameters with robust type checking and limits
            try:
                page_size = int(request.limit)
                if page_size <= 0:
                    page_size = 10
                elif page_size > 100:  # Add maximum limit
                    page_size = 100
            except (TypeError, ValueError):
                page_size = 10

            try:
                page = int(request.page)
                if page <= 0:
                    page = 1
            except (TypeError, ValueError):
                page = 1

            # Calculate normal pagination boundaries
            normal_skip = (page - 1) * page_size
            normal_end = normal_skip + page_size

            # Build enhanced message filter query with MongoDB Q objects
            from datetime import datetime

            from mongoengine import Q

            # Start with conversation filter (mandatory)
            query = Q(conversationId=request.conversationId)

            # Apply enhanced filters
            if message_type and message_type != 0:
                type_name = communication_pb2.MessageType.Name(message_type)
                query = query & Q(type=type_name)
                logger.info("Applying messageType filter", messageType=type_name)

            if sender_type and sender_type != 0:
                sender_name = communication_pb2.SenderType.Name(sender_type)
                query = query & Q(senderType=sender_name)
                logger.info("Applying senderType filter", senderType=sender_name)

            if status and status != 0:
                status_name = communication_pb2.MessageStatus.Name(status)
                query = query & Q(status=status_name)
                logger.info("Applying status filter", status=status_name)

            if workflow_id:
                query = query & Q(workflowId=workflow_id)
                logger.info("Applying workflowId filter", workflowId=workflow_id)

            # Date range filtering
            if date_from or date_to:
                if date_from:
                    # Convert timestamp to datetime if needed
                    if hasattr(date_from, "seconds"):
                        from_dt = datetime.fromtimestamp(date_from.seconds)
                    else:
                        from_dt = date_from
                    query = query & Q(createdAt__gte=from_dt)
                    logger.info("Applying dateFrom filter", dateFrom=from_dt)

                if date_to:
                    # Convert timestamp to datetime if needed
                    if hasattr(date_to, "seconds"):
                        to_dt = datetime.fromtimestamp(date_to.seconds)
                    else:
                        to_dt = date_to
                    query = query & Q(createdAt__lte=to_dt)
                    logger.info("Applying dateTo filter", dateTo=to_dt)

            # Determine sorting order
            sort_field = "-createdAt"  # Default: newest first
            if sort_by:
                sort_mapping = {
                    "CREATED_AT": "createdAt",
                    "UPDATED_AT": "updatedAt",
                    "MESSAGE_TYPE": "type",
                    "SENDER_TYPE": "senderType",
                    "STATUS": "status",
                }

                if sort_by in sort_mapping:
                    field = sort_mapping[sort_by]
                    if sort_order == "ASC":
                        sort_field = field
                    else:  # DESC or default
                        sort_field = f"-{field}"

                    logger.info(
                        "Applying custom sort",
                        sortBy=sort_by,
                        sortOrder=sort_order,
                        sortField=sort_field,
                    )

            # Apply field selection for performance optimization
            field_list = [
                "id",
                "conversationId",
                "senderType",
                "type",
                "status",
                "createdAt",
                "updatedAt",
            ]

            # Conditionally include data and workflow fields based on request
            if include_workflow_response:
                field_list.extend(["workflowId", "workflowResponse"])

            # Always include data field unless specifically excluded
            if not fields_only or "data" in fields_only:
                field_list.append("data")

            # Apply custom field selection if specified
            if fields_only:
                # Validate and filter requested fields
                valid_fields = [
                    "id",
                    "conversationId",
                    "senderType",
                    "data",
                    "workflowId",
                    "workflowResponse",
                    "status",
                    "type",
                    "createdAt",
                    "updatedAt",
                ]
                field_list = [f for f in fields_only if f in valid_fields]
                # Always include required fields
                required_fields = ["id", "conversationId", "senderType", "type", "createdAt"]
                field_list.extend([f for f in required_fields if f not in field_list])

            # Build optimized query with field selection and sorting
            message_filter = Message.objects(query).only(*field_list).order_by(sort_field)

            # Optimize count query - only count when necessary
            if page == 1:
                # Get one extra record to check if there are more pages
                temp_messages = list(message_filter[: page_size + 1])
                if len(temp_messages) <= page_size:
                    total_count = len(temp_messages)
                    total_pages = 1
                else:
                    # More records exist, do full count
                    total_count = message_filter.count()
                    total_pages = (total_count + page_size - 1) // page_size
            else:
                # For subsequent pages, do full count
                total_count = message_filter.count()
                total_pages = (total_count + page_size - 1) // page_size

            # Handle index-based retrieval if index is provided
            # Index behavior:
            # - If index is within page boundaries: use index as start point, keep page boundary as end
            # - If index is outside page boundaries: fall back to normal pagination
            # - Examples:
            #   * page=2, limit=10 (normal: 11-20), index=15: return 15-20 ✓
            #   * page=2, limit=10 (normal: 11-20), index=5: return 11-20 (fallback)
            #   * page=2, limit=10 (normal: 11-20), index=25: return 11-20 (fallback)
            if request.HasField("index"):
                try:
                    index = int(request.index)
                    if index >= 0:
                        # Check if index is within the normal page boundaries
                        if normal_skip <= index < normal_end:
                            # Index is within normal page range, use index as start
                            skip = index
                            end = normal_end
                            logger.info(
                                "Using index within page boundaries",
                                index=index,
                                normalSkip=normal_skip,
                                normalEnd=normal_end,
                                actualSkip=skip,
                                actualEnd=end,
                            )
                        elif index < normal_skip or index >= normal_end:
                            # Index is outside normal page range, check if it's reasonable
                            if index < total_count:
                                # Index is valid but outside page range, use normal pagination
                                skip = normal_skip
                                end = normal_end
                                logger.info(
                                    "Index outside page boundaries, using normal pagination",
                                    index=index,
                                    normalSkip=normal_skip,
                                    normalEnd=normal_end,
                                    totalCount=total_count,
                                )
                            else:
                                # Index is beyond total count, use normal pagination
                                skip = normal_skip
                                end = normal_end
                                logger.warning(
                                    "Index beyond total count, using normal pagination",
                                    index=index,
                                    totalCount=total_count,
                                )
                        else:
                            skip = normal_skip
                            end = normal_end
                    else:
                        # Negative index, use normal pagination
                        skip = normal_skip
                        end = normal_end
                        logger.warning(
                            "Negative index provided, using normal pagination",
                            index=index,
                        )
                except (TypeError, ValueError):
                    # Invalid index value, use normal pagination
                    skip = normal_skip
                    end = normal_end
                    logger.warning(
                        "Invalid index value, using normal pagination",
                        indexValue=request.index,
                    )
            else:
                # No index provided, use normal pagination
                skip = normal_skip
                end = normal_end

            # Get paginated results
            messages = message_filter[skip:end]

            # Log success
            logger.info("Messages listed successfully", conversationId=request.conversationId)

            # Build pagination metadata
            metadata = communication_pb2.PaginationMetadata(
                total=total_count,
                totalPages=total_pages,
                currentPage=page,
                pageSize=page_size,
                hasNextPage=page < total_pages,
                hasPreviousPage=page > 1,
            )

            # Convert to protobuf messages with debug logging for JSON data
            proto_messages = []
            for idx, message in enumerate(messages):
                try:
                    proto = message.to_proto()
                    if not hasattr(proto, "DESCRIPTOR"):
                        logger.error(
                            "to_proto() did not return a protobuf message",
                            index=idx,
                            type=str(type(proto)),
                            value=str(proto),
                        )
                    proto_messages.append(proto)
                except Exception as e:
                    logger.error(
                        "Exception in message.to_proto()",
                        index=idx,
                        error=str(e),
                        message_repr=repr(message),
                    )

            # Apply response optimization if requested
            optimize_response = (
                getattr(request, "optimizeResponse", False)
                if hasattr(request, "optimizeResponse")
                else False
            )
            exclude_fields_req = (
                getattr(request, "excludeFields", None)
                if hasattr(request, "excludeFields")
                else None
            )
            include_fields_req = (
                getattr(request, "includeFields", None)
                if hasattr(request, "includeFields")
                else None
            )

            if optimize_response or exclude_fields_req or include_fields_req:
                # Convert to set for faster lookup
                exclude_set = set(exclude_fields_req) if exclude_fields_req else None
                include_set = set(include_fields_req) if include_fields_req else None

                # Apply field filtering to message protos
                optimized_protos = ResponseOptimizer.optimize_list_response(
                    proto_messages, include_fields=include_set, exclude_fields=exclude_set
                )

                logger.info(
                    "Applied response optimization to messages",
                    originalCount=len(proto_messages),
                    optimizedCount=len(optimized_protos),
                    includeFields=include_fields_req,
                    excludeFields=exclude_fields_req,
                    optimizeResponse=optimize_response,
                )

                # For now, we'll keep the original protos but log the optimization
                # In a full implementation, you'd need to reconstruct protobuf messages
                # from the optimized dictionaries

            # Monitor response size for performance insights
            ResponseOptimizer.monitor_response_size(proto_messages, "listMessages")

            logger.info(
                "Returning ListMessagesResponse with enhanced optimization support",
                proto_types=[str(type(m)) for m in proto_messages],
                count=len(proto_messages),
                fieldsFiltered=bool(exclude_fields_req or include_fields_req),
                optimizationRequested=optimize_response,
            )
            return communication_pb2.ListMessagesResponse(
                data=proto_messages,
                metadata=metadata,
            )

        except Exception as e:
            # Log error with enhanced filter context
            logger.error(
                "Failed to list messages with enhanced filters",
                error=str(e),
                conversationId=request.conversationId,
                userId=request.userId,
                messageType=(
                    getattr(request, "messageType", None)
                    if hasattr(request, "messageType")
                    else None
                ),
                senderType=(
                    getattr(request, "senderType", None) if hasattr(request, "senderType") else None
                ),
                status=getattr(request, "status", None) if hasattr(request, "status") else None,
                workflowId=(
                    getattr(request, "workflowId", None) if hasattr(request, "workflowId") else None
                ),
                sortBy=getattr(request, "sortBy", None) if hasattr(request, "sortBy") else None,
                sortOrder=(
                    getattr(request, "sortOrder", None) if hasattr(request, "sortOrder") else None
                ),
            )

            # Raise gRPC exception
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to list messages: {str(e)}")

    def _extract_title_from_message_data(self, message_data):
        """
        Extract a title from message data.

        Args:
            message_data: Dictionary containing message data

        Returns:
            str: Extracted title or None
        """
        if not message_data:
            return None

        # Try to extract title from common fields
        if isinstance(message_data, dict):
            # Look for common title fields
            for field in ["title", "subject", "content", "text", "message"]:
                if field in message_data and message_data[field]:
                    title = str(message_data[field]).strip()
                    # Truncate if too long
                    if len(title) > 100:
                        title = title[:97] + "..."
                    return title

            # If no specific title field, try to extract from first available text field
            for key, value in message_data.items():
                if isinstance(value, str) and value.strip():
                    title = value.strip()
                    # Truncate if too long
                    if len(title) > 100:
                        title = title[:97] + "..."
                    return title

        return None

    # Update message workflow response
    def updateMessageWorkflowResponse(
        self,
        request: communication_pb2.UpdateMessageWorkflowResponseRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Message:
        """
        Update the workflowResponse array of a message by appending new data.

        Args:
            request: UpdateMessageWorkflowResponseRequest containing message ID, new workflow response data, and user ID
            context: gRPC servicer context

        Returns:
            Updated message if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Updating message workflow response",
                messageId=request.messageId,
                userId=request.userId,
                hasNewResponse=bool(request.HasField("newWorkflowResponse")),
            )

            # Get the message
            try:
                message = Message.objects.get(id=request.messageId)
            except Message.DoesNotExist:
                logger.error("Message not found", messageId=request.messageId)
                context.abort(grpc.StatusCode.NOT_FOUND, f"Message {request.messageId} not found")

            # Check if the user is authorized to update this message
            try:
                conversation = Conversation.objects.get(id=message.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message workflow response update attempt",
                        messageId=request.messageId,
                        conversationId=str(message.conversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to update message {request.messageId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=str(message.conversationId))
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {message.conversationId} not found",
                )

            # Process the new workflow response data
            if request.HasField("newWorkflowResponse"):
                try:
                    # Initialize workflowResponse as empty list if it's None
                    if message.workflowResponse is None:
                        message.workflowResponse = []

                    # Convert protobuf Struct (JSON) to Python dict
                    from google.protobuf.json_format import MessageToDict

                    workflow_data = MessageToDict(request.newWorkflowResponse)

                    # Append the JSON data to the array
                    message.workflowResponse.append(workflow_data)

                    # Save the updated message
                    message.save()

                    logger.info(
                        "Message workflow response updated successfully",
                        messageId=request.messageId,
                        newArrayLength=len(message.workflowResponse),
                    )

                except Exception as e:
                    logger.error(
                        "Failed to process new workflow response",
                        messageId=request.messageId,
                        error=str(e),
                    )
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT,
                        f"Invalid workflow response data: {str(e)}",
                    )
            else:
                logger.warning(
                    "No new workflow response data provided",
                    messageId=request.messageId,
                )
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    "No new workflow response data provided",
                )

            # Convert to protobuf message and return
            return message.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to update message workflow response", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL,
                f"Failed to update message workflow response: {str(e)}",
            )

    # Update message status
    def updateMessageStatus(
        self,
        request: communication_pb2.UpdateMessageStatusRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Message:
        """
        Update the status of a message.

        Args:
            request: UpdateMessageStatusRequest containing message ID, new status, and user ID
            context: gRPC servicer context

        Returns:
            Updated message if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Updating message status",
                messageId=request.messageId,
                userId=request.userId,
                newStatus=communication_pb2.MessageStatus.Name(request.status),
            )

            # Get the message
            try:
                message = Message.objects.get(id=request.messageId)
            except Message.DoesNotExist:
                logger.error("Message not found", messageId=request.messageId)
                context.abort(grpc.StatusCode.NOT_FOUND, f"Message {request.messageId} not found")

            # Check if the user is authorized to update this message
            try:
                conversation = Conversation.objects.get(id=message.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message status update attempt",
                        messageId=request.messageId,
                        conversationId=str(message.conversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to update message {request.messageId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=str(message.conversationId))
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {message.conversationId} not found",
                )

            # Convert int enum to string enum name for storage
            status_name = communication_pb2.MessageStatus.Name(request.status)

            # Validate that the status is not UNSPECIFIED
            if status_name == "MESSAGE_STATUS_UNSPECIFIED":
                logger.warning(
                    "Attempt to set message status to UNSPECIFIED",
                    messageId=request.messageId,
                    userId=request.userId,
                )
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    "Cannot set message status to MESSAGE_STATUS_UNSPECIFIED",
                )

            # Update the message status
            old_status = message.status
            message.status = status_name

            # Save the updated message
            message.save()

            logger.info(
                "Message status updated successfully",
                messageId=request.messageId,
                oldStatus=old_status,
                newStatus=status_name,
            )

            # Convert to protobuf message and return
            return message.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to update message status", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL,
                f"Failed to update message status: {str(e)}",
            )
