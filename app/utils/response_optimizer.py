"""
Response optimization utilities for the communication service.

This module provides utilities for optimizing API responses including:
- Data compression for large responses
- Field selection and filtering
- Conditional loading of related entities
- Response size monitoring and optimization
"""

# Standard library imports
import gzip
import json
import sys
from typing import Any, Dict, List, Optional, Set, Union

# Third-party imports
from google.protobuf import message as protobuf_message
from google.protobuf.json_format import MessageToDict, MessageToJson

# Local imports
from app.utils.logger import setup_logger

logger = setup_logger("communication-service/utils/response_optimizer.py")


class ResponseOptimizer:
    """
    Utility class for optimizing API responses with compression and field selection.
    """

    # Default compression threshold (responses larger than this will be compressed)
    DEFAULT_COMPRESSION_THRESHOLD = 1024  # 1KB

    # Maximum response size before warning (10MB)
    MAX_RESPONSE_SIZE_WARNING = 10 * 1024 * 1024

    @staticmethod
    def compress_response_data(data: bytes, compression_threshold: int = None) -> tuple[bytes, bool]:
        """
        Compress response data if it exceeds the threshold.

        Args:
            data: Raw response data as bytes
            compression_threshold: Size threshold for compression (default: 1KB)

        Returns:
            Tuple of (compressed_data, was_compressed)
        """
        if compression_threshold is None:
            compression_threshold = ResponseOptimizer.DEFAULT_COMPRESSION_THRESHOLD

        original_size = len(data)

        # Only compress if data exceeds threshold
        if original_size > compression_threshold:
            try:
                compressed_data = gzip.compress(data)
                compressed_size = len(compressed_data)
                
                # Only use compression if it actually reduces size significantly
                compression_ratio = compressed_size / original_size
                if compression_ratio < 0.9:  # At least 10% reduction
                    logger.info(
                        "Response compressed",
                        originalSize=original_size,
                        compressedSize=compressed_size,
                        compressionRatio=f"{compression_ratio:.2f}",
                        savings=f"{(1 - compression_ratio) * 100:.1f}%"
                    )
                    return compressed_data, True
                else:
                    logger.debug(
                        "Compression not beneficial",
                        originalSize=original_size,
                        compressedSize=compressed_size,
                        compressionRatio=f"{compression_ratio:.2f}"
                    )
                    return data, False
            except Exception as e:
                logger.error("Failed to compress response", error=str(e), originalSize=original_size)
                return data, False
        
        return data, False

    @staticmethod
    def filter_protobuf_fields(
        proto_message: protobuf_message.Message,
        include_fields: Optional[Set[str]] = None,
        exclude_fields: Optional[Set[str]] = None
    ) -> Dict[str, Any]:
        """
        Filter protobuf message fields based on include/exclude lists.

        Args:
            proto_message: Protobuf message to filter
            include_fields: Set of field names to include (if None, include all)
            exclude_fields: Set of field names to exclude

        Returns:
            Filtered dictionary representation of the message
        """
        try:
            # Convert protobuf to dictionary
            message_dict = MessageToDict(proto_message, preserving_proto_field_name=True)
            
            # Apply field filtering
            if include_fields is not None:
                # Only include specified fields
                filtered_dict = {k: v for k, v in message_dict.items() if k in include_fields}
            else:
                # Include all fields except excluded ones
                filtered_dict = message_dict.copy()
                
            if exclude_fields is not None:
                # Remove excluded fields
                for field in exclude_fields:
                    filtered_dict.pop(field, None)
            
            return filtered_dict
            
        except Exception as e:
            logger.error(
                "Failed to filter protobuf fields",
                error=str(e),
                messageType=type(proto_message).__name__
            )
            # Return original message as dict if filtering fails
            return MessageToDict(proto_message, preserving_proto_field_name=True)

    @staticmethod
    def optimize_list_response(
        items: List[Any],
        include_fields: Optional[Set[str]] = None,
        exclude_fields: Optional[Set[str]] = None,
        max_items: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Optimize a list response by filtering fields and limiting items.

        Args:
            items: List of protobuf messages or dictionaries
            include_fields: Set of field names to include
            exclude_fields: Set of field names to exclude
            max_items: Maximum number of items to return

        Returns:
            Optimized list of dictionaries
        """
        try:
            # Limit number of items if specified
            if max_items is not None and len(items) > max_items:
                items = items[:max_items]
                logger.info("Response truncated", originalCount=len(items), maxItems=max_items)

            optimized_items = []
            
            for item in items:
                if hasattr(item, 'DESCRIPTOR'):  # Protobuf message
                    filtered_item = ResponseOptimizer.filter_protobuf_fields(
                        item, include_fields, exclude_fields
                    )
                elif isinstance(item, dict):
                    # Already a dictionary, apply field filtering
                    if include_fields is not None:
                        filtered_item = {k: v for k, v in item.items() if k in include_fields}
                    else:
                        filtered_item = item.copy()
                        
                    if exclude_fields is not None:
                        for field in exclude_fields:
                            filtered_item.pop(field, None)
                else:
                    # Unknown type, keep as-is
                    filtered_item = item
                
                optimized_items.append(filtered_item)
            
            return optimized_items
            
        except Exception as e:
            logger.error("Failed to optimize list response", error=str(e), itemCount=len(items))
            # Return original items if optimization fails
            return items

    @staticmethod
    def calculate_response_size(data: Any) -> int:
        """
        Calculate the approximate size of response data.

        Args:
            data: Response data (protobuf, dict, list, etc.)

        Returns:
            Approximate size in bytes
        """
        try:
            if hasattr(data, 'DESCRIPTOR'):  # Protobuf message
                json_str = MessageToJson(data)
                return len(json_str.encode('utf-8'))
            elif isinstance(data, (dict, list)):
                json_str = json.dumps(data)
                return len(json_str.encode('utf-8'))
            elif isinstance(data, str):
                return len(data.encode('utf-8'))
            elif isinstance(data, bytes):
                return len(data)
            else:
                # Fallback: convert to string and measure
                return len(str(data).encode('utf-8'))
        except Exception as e:
            logger.error("Failed to calculate response size", error=str(e))
            return 0

    @staticmethod
    def monitor_response_size(data: Any, operation: str = "unknown") -> None:
        """
        Monitor and log response size, warning if it's too large.

        Args:
            data: Response data to monitor
            operation: Name of the operation for logging
        """
        try:
            size = ResponseOptimizer.calculate_response_size(data)
            
            if size > ResponseOptimizer.MAX_RESPONSE_SIZE_WARNING:
                logger.warning(
                    "Large response detected",
                    operation=operation,
                    responseSize=size,
                    responseSizeMB=f"{size / (1024 * 1024):.2f}",
                    recommendation="Consider implementing pagination or field filtering"
                )
            else:
                logger.debug(
                    "Response size monitored",
                    operation=operation,
                    responseSize=size,
                    responseSizeKB=f"{size / 1024:.2f}"
                )
                
        except Exception as e:
            logger.error("Failed to monitor response size", error=str(e), operation=operation)

    @staticmethod
    def create_optimized_metadata(
        total_items: int,
        returned_items: int,
        fields_filtered: bool = False,
        compressed: bool = False,
        optimization_applied: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create metadata about response optimizations applied.

        Args:
            total_items: Total number of items available
            returned_items: Number of items actually returned
            fields_filtered: Whether field filtering was applied
            compressed: Whether response was compressed
            optimization_applied: Description of optimizations applied

        Returns:
            Optimization metadata dictionary
        """
        metadata = {
            "totalItems": total_items,
            "returnedItems": returned_items,
            "fieldsFiltered": fields_filtered,
            "compressed": compressed
        }
        
        if optimization_applied:
            metadata["optimizationApplied"] = optimization_applied
            
        if returned_items < total_items:
            metadata["truncated"] = True
            metadata["truncationRatio"] = returned_items / total_items
            
        return metadata
