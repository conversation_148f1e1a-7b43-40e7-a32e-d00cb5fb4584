"""
Query caching utilities for database operations.

This module provides caching specifically for database queries including:
- Automatic cache key generation from query parameters
- Cache invalidation based on data changes
- Query result serialization and deserialization
- Performance monitoring for cached vs uncached queries
"""

# Standard library imports
import hashlib
import json
import time
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Local imports
from app.utils.cache_manager import get_cache_manager
from app.utils.logger import setup_logger

logger = setup_logger("communication-service/utils/query_cache.py")


class QueryCache:
    """
    Database query caching utility.
    """

    def __init__(self, cache_manager=None, default_ttl: int = 1800):  # 30 minutes default
        """
        Initialize query cache.

        Args:
            cache_manager: Cache manager instance (uses global if None)
            default_ttl: Default TTL for cached queries in seconds
        """
        self.cache_manager = cache_manager or get_cache_manager()
        self.default_ttl = default_ttl

    def _generate_cache_key(
        self,
        query_type: str,
        user_id: str,
        filters: Dict[str, Any],
        sort_params: Optional[Dict[str, Any]] = None,
        pagination: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Generate a unique cache key for a query.

        Args:
            query_type: Type of query (e.g., "conversations", "messages", "tasks")
            user_id: User ID for the query
            filters: Query filters
            sort_params: Sorting parameters
            pagination: Pagination parameters

        Returns:
            Unique cache key
        """
        # Create a deterministic representation of the query
        query_data = {
            "query_type": query_type,
            "user_id": user_id,
            "filters": filters,
            "sort_params": sort_params or {},
            "pagination": pagination or {}
        }
        
        # Sort keys to ensure consistent ordering
        query_json = json.dumps(query_data, sort_keys=True, default=str)
        
        # Generate hash for the query
        query_hash = hashlib.md5(query_json.encode()).hexdigest()
        
        # Build cache key
        cache_key = f"query:{query_type}:{user_id}:{query_hash}"
        
        logger.debug(
            "Generated cache key",
            queryType=query_type,
            userId=user_id,
            cacheKey=cache_key,
            queryHash=query_hash
        )
        
        return cache_key

    def get_cached_query_result(
        self,
        query_type: str,
        user_id: str,
        filters: Dict[str, Any],
        sort_params: Optional[Dict[str, Any]] = None,
        pagination: Optional[Dict[str, Any]] = None
    ) -> Optional[Tuple[List[Any], Dict[str, Any]]]:
        """
        Get cached query result if available.

        Args:
            query_type: Type of query
            user_id: User ID
            filters: Query filters
            sort_params: Sorting parameters
            pagination: Pagination parameters

        Returns:
            Tuple of (results, metadata) if cached, None otherwise
        """
        cache_key = self._generate_cache_key(
            query_type, user_id, filters, sort_params, pagination
        )
        
        start_time = time.time()
        cached_result = self.cache_manager.get(cache_key)
        cache_time = time.time() - start_time
        
        if cached_result is not None:
            logger.info(
                "Cache hit for query",
                queryType=query_type,
                userId=user_id,
                cacheKey=cache_key,
                cacheRetrievalTime=round(cache_time * 1000, 2)  # ms
            )
            
            # Cached result should be a tuple of (results, metadata)
            if isinstance(cached_result, (list, tuple)) and len(cached_result) == 2:
                return cached_result
            else:
                logger.warning(
                    "Invalid cached result format",
                    queryType=query_type,
                    cacheKey=cache_key,
                    resultType=type(cached_result)
                )
                # Remove invalid cache entry
                self.cache_manager.delete(cache_key)
                return None
        else:
            logger.debug(
                "Cache miss for query",
                queryType=query_type,
                userId=user_id,
                cacheKey=cache_key
            )
            return None

    def cache_query_result(
        self,
        query_type: str,
        user_id: str,
        filters: Dict[str, Any],
        results: List[Any],
        metadata: Dict[str, Any],
        sort_params: Optional[Dict[str, Any]] = None,
        pagination: Optional[Dict[str, Any]] = None,
        ttl: Optional[int] = None
    ) -> bool:
        """
        Cache query results.

        Args:
            query_type: Type of query
            user_id: User ID
            filters: Query filters
            results: Query results to cache
            metadata: Query metadata (pagination, etc.)
            sort_params: Sorting parameters
            pagination: Pagination parameters
            ttl: Time-to-live in seconds

        Returns:
            True if cached successfully, False otherwise
        """
        cache_key = self._generate_cache_key(
            query_type, user_id, filters, sort_params, pagination
        )
        
        # Prepare data for caching
        cache_data = (results, metadata)
        
        start_time = time.time()
        success = self.cache_manager.set(
            cache_key,
            cache_data,
            ttl=ttl or self.default_ttl,
            serialize_method="pickle"  # Use pickle for complex objects
        )
        cache_time = time.time() - start_time
        
        if success:
            logger.info(
                "Cached query result",
                queryType=query_type,
                userId=user_id,
                cacheKey=cache_key,
                resultCount=len(results),
                cacheStorageTime=round(cache_time * 1000, 2),  # ms
                ttl=ttl or self.default_ttl
            )
        else:
            logger.warning(
                "Failed to cache query result",
                queryType=query_type,
                userId=user_id,
                cacheKey=cache_key
            )
        
        return success

    def invalidate_user_cache(self, user_id: str, query_types: Optional[List[str]] = None) -> int:
        """
        Invalidate all cached queries for a user.

        Args:
            user_id: User ID
            query_types: Specific query types to invalidate (all if None)

        Returns:
            Number of cache entries invalidated
        """
        if query_types:
            total_deleted = 0
            for query_type in query_types:
                pattern = f"query:{query_type}:{user_id}:*"
                deleted = self.cache_manager.delete_pattern(pattern)
                total_deleted += deleted
                
            logger.info(
                "Invalidated user cache for specific query types",
                userId=user_id,
                queryTypes=query_types,
                deletedEntries=total_deleted
            )
        else:
            pattern = f"query:*:{user_id}:*"
            total_deleted = self.cache_manager.delete_pattern(pattern)
            
            logger.info(
                "Invalidated all user cache",
                userId=user_id,
                deletedEntries=total_deleted
            )
        
        return total_deleted

    def invalidate_query_type_cache(self, query_type: str) -> int:
        """
        Invalidate all cached queries of a specific type.

        Args:
            query_type: Query type to invalidate

        Returns:
            Number of cache entries invalidated
        """
        pattern = f"query:{query_type}:*"
        deleted = self.cache_manager.delete_pattern(pattern)
        
        logger.info(
            "Invalidated query type cache",
            queryType=query_type,
            deletedEntries=deleted
        )
        
        return deleted

    def get_cache_stats(self, query_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Get cache statistics for queries.

        Args:
            query_type: Specific query type to get stats for (all if None)

        Returns:
            Dictionary containing cache statistics
        """
        base_stats = self.cache_manager.get_cache_stats()
        
        # Add query-specific stats
        query_stats = {
            "default_ttl": self.default_ttl,
            "query_type_filter": query_type
        }
        
        # Combine stats
        base_stats.update(query_stats)
        
        return base_stats


def cached_query(
    query_type: str,
    ttl: Optional[int] = None,
    cache_condition: Optional[Callable] = None
):
    """
    Decorator for caching database queries.

    Args:
        query_type: Type of query for cache key generation
        ttl: Time-to-live in seconds
        cache_condition: Optional function to determine if result should be cached

    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            # Extract user_id and other parameters from function arguments
            # This assumes the function signature includes user_id and request parameters
            
            # Try to extract user_id from various possible locations
            user_id = None
            filters = {}
            sort_params = {}
            pagination = {}
            
            # Look for user_id in args (typically in request object)
            for arg in args:
                if hasattr(arg, 'userId'):
                    user_id = arg.userId
                    break
                elif hasattr(arg, 'user_id'):
                    user_id = arg.user_id
                    break
            
            # Look for user_id in kwargs
            if not user_id:
                user_id = kwargs.get('user_id') or kwargs.get('userId')
            
            # If we can't find user_id, skip caching
            if not user_id:
                logger.debug(f"No user_id found for caching {func.__name__}, executing directly")
                return func(*args, **kwargs)
            
            # Extract request parameters for cache key generation
            for arg in args:
                if hasattr(arg, 'chatType'):
                    filters['chatType'] = arg.chatType
                if hasattr(arg, 'agentId'):
                    filters['agentId'] = arg.agentId
                if hasattr(arg, 'search'):
                    filters['search'] = getattr(arg, 'search', None)
                if hasattr(arg, 'limit'):
                    pagination['limit'] = arg.limit
                if hasattr(arg, 'offset'):
                    pagination['offset'] = arg.offset
                if hasattr(arg, 'sortBy'):
                    sort_params['sortBy'] = getattr(arg, 'sortBy', None)
                if hasattr(arg, 'sortOrder'):
                    sort_params['sortOrder'] = getattr(arg, 'sortOrder', None)
            
            # Initialize query cache
            query_cache = QueryCache()
            
            # Try to get cached result
            cached_result = query_cache.get_cached_query_result(
                query_type=query_type,
                user_id=user_id,
                filters=filters,
                sort_params=sort_params,
                pagination=pagination
            )
            
            if cached_result is not None:
                return cached_result
            
            # Execute the original function
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            logger.info(
                f"Executed {func.__name__}",
                executionTime=round(execution_time * 1000, 2),  # ms
                queryType=query_type,
                userId=user_id
            )
            
            # Cache the result if conditions are met
            should_cache = True
            if cache_condition:
                should_cache = cache_condition(result)
            
            if should_cache and result:
                # Assume result is a response object with data and metadata
                if hasattr(result, 'data') and hasattr(result, 'metadata'):
                    query_cache.cache_query_result(
                        query_type=query_type,
                        user_id=user_id,
                        filters=filters,
                        results=list(result.data),
                        metadata={
                            'total': result.metadata.total,
                            'totalPages': result.metadata.totalPages,
                            'currentPage': result.metadata.currentPage,
                            'pageSize': result.metadata.pageSize,
                            'hasNextPage': result.metadata.hasNextPage,
                            'hasPreviousPage': result.metadata.hasPreviousPage
                        },
                        sort_params=sort_params,
                        pagination=pagination,
                        ttl=ttl
                    )
            
            return result
        
        return wrapper
    return decorator


# Global query cache instance
query_cache = QueryCache()
