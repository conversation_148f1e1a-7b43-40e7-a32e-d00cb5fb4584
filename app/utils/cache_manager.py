"""
Cache management utilities for the communication service.

This module provides caching capabilities including:
- Redis-based caching for frequently accessed data
- Query result caching with TTL support
- Cache invalidation strategies
- Performance monitoring and metrics
"""

# Standard library imports
import json
import pickle
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import redis
from redis.exceptions import ConnectionError, RedisError

# Local imports
from app.utils.logger import setup_logger

logger = setup_logger("communication-service/utils/cache_manager.py")


class CacheManager:
    """
    Centralized cache management using Redis.
    """

    def __init__(
        self,
        redis_host: str = "localhost",
        redis_port: int = 6379,
        redis_db: int = 0,
        redis_password: Optional[str] = None,
        default_ttl: int = 3600,  # 1 hour default TTL
        key_prefix: str = "comm_service:"
    ):
        """
        Initialize the cache manager.

        Args:
            redis_host: Redis server hostname
            redis_port: Redis server port
            redis_db: Redis database number
            redis_password: Redis password (if required)
            default_ttl: Default time-to-live in seconds
            key_prefix: Prefix for all cache keys
        """
        self.redis_host = redis_host
        self.redis_port = redis_port
        self.redis_db = redis_db
        self.redis_password = redis_password
        self.default_ttl = default_ttl
        self.key_prefix = key_prefix
        self._redis_client = None
        self._connection_healthy = False

    @property
    def redis_client(self) -> Optional[redis.Redis]:
        """
        Get Redis client with connection health checking.

        Returns:
            Redis client instance or None if connection fails
        """
        if self._redis_client is None or not self._connection_healthy:
            try:
                self._redis_client = redis.Redis(
                    host=self.redis_host,
                    port=self.redis_port,
                    db=self.redis_db,
                    password=self.redis_password,
                    decode_responses=False,  # We'll handle encoding ourselves
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
                
                # Test connection
                self._redis_client.ping()
                self._connection_healthy = True
                logger.info("Redis connection established successfully")
                
            except (ConnectionError, RedisError) as e:
                logger.warning(f"Failed to connect to Redis: {str(e)}")
                self._connection_healthy = False
                return None
                
        return self._redis_client

    def _build_key(self, key: str) -> str:
        """
        Build a full cache key with prefix.

        Args:
            key: Base key name

        Returns:
            Full cache key with prefix
        """
        return f"{self.key_prefix}{key}"

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a value from cache.

        Args:
            key: Cache key
            default: Default value if key not found

        Returns:
            Cached value or default
        """
        if not self.redis_client:
            return default

        try:
            full_key = self._build_key(key)
            cached_data = self.redis_client.get(full_key)
            
            if cached_data is None:
                logger.debug(f"Cache miss for key: {key}")
                return default
            
            # Try to deserialize the data
            try:
                # First try JSON (for simple data types)
                value = json.loads(cached_data.decode('utf-8'))
                logger.debug(f"Cache hit (JSON) for key: {key}")
                return value
            except (json.JSONDecodeError, UnicodeDecodeError):
                # Fall back to pickle for complex objects
                try:
                    value = pickle.loads(cached_data)
                    logger.debug(f"Cache hit (pickle) for key: {key}")
                    return value
                except pickle.PickleError:
                    logger.warning(f"Failed to deserialize cached data for key: {key}")
                    return default
                    
        except RedisError as e:
            logger.warning(f"Redis error getting key {key}: {str(e)}")
            return default

    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        serialize_method: str = "auto"
    ) -> bool:
        """
        Set a value in cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (uses default if None)
            serialize_method: Serialization method ("json", "pickle", "auto")

        Returns:
            True if successful, False otherwise
        """
        if not self.redis_client:
            return False

        try:
            full_key = self._build_key(key)
            ttl = ttl or self.default_ttl
            
            # Serialize the value
            if serialize_method == "auto":
                # Try JSON first for better performance and readability
                try:
                    serialized_data = json.dumps(value).encode('utf-8')
                    serialize_method = "json"
                except (TypeError, ValueError):
                    # Fall back to pickle for complex objects
                    serialized_data = pickle.dumps(value)
                    serialize_method = "pickle"
            elif serialize_method == "json":
                serialized_data = json.dumps(value).encode('utf-8')
            elif serialize_method == "pickle":
                serialized_data = pickle.dumps(value)
            else:
                raise ValueError(f"Invalid serialize_method: {serialize_method}")
            
            # Set the value with TTL
            result = self.redis_client.setex(full_key, ttl, serialized_data)
            
            logger.debug(
                f"Cached value for key: {key}",
                ttl=ttl,
                serializeMethod=serialize_method,
                dataSize=len(serialized_data)
            )
            
            return result
            
        except (RedisError, ValueError, TypeError) as e:
            logger.warning(f"Failed to cache value for key {key}: {str(e)}")
            return False

    def delete(self, key: str) -> bool:
        """
        Delete a value from cache.

        Args:
            key: Cache key to delete

        Returns:
            True if successful, False otherwise
        """
        if not self.redis_client:
            return False

        try:
            full_key = self._build_key(key)
            result = self.redis_client.delete(full_key)
            logger.debug(f"Deleted cache key: {key}", deleted=bool(result))
            return bool(result)
            
        except RedisError as e:
            logger.warning(f"Failed to delete cache key {key}: {str(e)}")
            return False

    def delete_pattern(self, pattern: str) -> int:
        """
        Delete all keys matching a pattern.

        Args:
            pattern: Key pattern (supports wildcards)

        Returns:
            Number of keys deleted
        """
        if not self.redis_client:
            return 0

        try:
            full_pattern = self._build_key(pattern)
            keys = self.redis_client.keys(full_pattern)
            
            if keys:
                deleted_count = self.redis_client.delete(*keys)
                logger.info(f"Deleted {deleted_count} keys matching pattern: {pattern}")
                return deleted_count
            else:
                logger.debug(f"No keys found matching pattern: {pattern}")
                return 0
                
        except RedisError as e:
            logger.warning(f"Failed to delete keys with pattern {pattern}: {str(e)}")
            return 0

    def exists(self, key: str) -> bool:
        """
        Check if a key exists in cache.

        Args:
            key: Cache key to check

        Returns:
            True if key exists, False otherwise
        """
        if not self.redis_client:
            return False

        try:
            full_key = self._build_key(key)
            return bool(self.redis_client.exists(full_key))
            
        except RedisError as e:
            logger.warning(f"Failed to check existence of key {key}: {str(e)}")
            return False

    def get_ttl(self, key: str) -> int:
        """
        Get the time-to-live for a key.

        Args:
            key: Cache key

        Returns:
            TTL in seconds, -1 if key has no expiry, -2 if key doesn't exist
        """
        if not self.redis_client:
            return -2

        try:
            full_key = self._build_key(key)
            return self.redis_client.ttl(full_key)
            
        except RedisError as e:
            logger.warning(f"Failed to get TTL for key {key}: {str(e)}")
            return -2

    def extend_ttl(self, key: str, additional_seconds: int) -> bool:
        """
        Extend the TTL of an existing key.

        Args:
            key: Cache key
            additional_seconds: Additional seconds to add to TTL

        Returns:
            True if successful, False otherwise
        """
        if not self.redis_client:
            return False

        try:
            full_key = self._build_key(key)
            current_ttl = self.redis_client.ttl(full_key)
            
            if current_ttl > 0:
                new_ttl = current_ttl + additional_seconds
                result = self.redis_client.expire(full_key, new_ttl)
                logger.debug(f"Extended TTL for key: {key}", newTtl=new_ttl)
                return result
            else:
                logger.debug(f"Key {key} has no TTL or doesn't exist")
                return False
                
        except RedisError as e:
            logger.warning(f"Failed to extend TTL for key {key}: {str(e)}")
            return False

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics and health information.

        Returns:
            Dictionary containing cache statistics
        """
        stats = {
            "connection_healthy": self._connection_healthy,
            "redis_host": self.redis_host,
            "redis_port": self.redis_port,
            "redis_db": self.redis_db,
            "default_ttl": self.default_ttl,
            "key_prefix": self.key_prefix
        }

        if self.redis_client and self._connection_healthy:
            try:
                info = self.redis_client.info()
                stats.update({
                    "redis_version": info.get("redis_version"),
                    "used_memory": info.get("used_memory"),
                    "used_memory_human": info.get("used_memory_human"),
                    "connected_clients": info.get("connected_clients"),
                    "total_commands_processed": info.get("total_commands_processed"),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0)
                })
                
                # Calculate hit rate
                hits = stats.get("keyspace_hits", 0)
                misses = stats.get("keyspace_misses", 0)
                total_requests = hits + misses
                
                if total_requests > 0:
                    stats["hit_rate"] = round((hits / total_requests) * 100, 2)
                else:
                    stats["hit_rate"] = 0.0
                    
            except RedisError as e:
                logger.warning(f"Failed to get Redis info: {str(e)}")
                stats["error"] = str(e)

        return stats


# Global cache manager instance
cache_manager = CacheManager()


def get_cache_manager() -> CacheManager:
    """
    Get the global cache manager instance.

    Returns:
        CacheManager instance
    """
    return cache_manager
