"""
Advanced search utilities for the communication service.

This module provides enhanced search capabilities including:
- Multi-field search across conversations, messages, and tasks
- Full-text search with relevance scoring
- Advanced filtering with complex query building
- Search result optimization and ranking
"""

# Standard library imports
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple, Union

# Third-party imports
from mongoengine import Q

# Local imports
from app.utils.logger import setup_logger

logger = setup_logger("communication-service/utils/advanced_search.py")


class AdvancedSearchBuilder:
    """
    Utility class for building advanced search queries with MongoDB.
    """

    @staticmethod
    def build_text_search_query(
        search_term: str,
        fields: List[str],
        case_sensitive: bool = False,
        exact_match: bool = False
    ) -> Q:
        """
        Build a text search query across multiple fields.

        Args:
            search_term: The term to search for
            fields: List of field names to search in
            case_sensitive: Whether search should be case sensitive
            exact_match: Whether to search for exact matches only

        Returns:
            MongoDB Q query object
        """
        if not search_term or not search_term.strip():
            return Q()

        # Escape special regex characters to prevent injection
        escaped_term = re.escape(search_term.strip())
        
        if exact_match:
            # Exact match search
            if case_sensitive:
                pattern = f"^{escaped_term}$"
                regex_option = "regex"
            else:
                pattern = f"^{escaped_term}$"
                regex_option = "iregex"
        else:
            # Partial match search
            pattern = f".*{escaped_term}.*"
            regex_option = "iregex" if not case_sensitive else "regex"

        # Build OR query across all specified fields
        field_queries = []
        for field in fields:
            field_query = Q(**{f"{field}__{regex_option}": pattern})
            field_queries.append(field_query)

        if field_queries:
            # Combine with OR logic
            combined_query = field_queries[0]
            for query in field_queries[1:]:
                combined_query = combined_query | query
            
            logger.debug(
                "Built text search query",
                searchTerm=search_term,
                fields=fields,
                caseSensitive=case_sensitive,
                exactMatch=exact_match,
                pattern=pattern
            )
            
            return combined_query
        
        return Q()

    @staticmethod
    def build_date_range_query(
        field_name: str,
        date_from: Optional[Union[datetime, Any]] = None,
        date_to: Optional[Union[datetime, Any]] = None
    ) -> Q:
        """
        Build a date range query for a specific field.

        Args:
            field_name: Name of the date field to filter on
            date_from: Start date (inclusive)
            date_to: End date (inclusive)

        Returns:
            MongoDB Q query object
        """
        query = Q()
        
        if date_from:
            # Convert timestamp to datetime if needed
            if hasattr(date_from, 'seconds'):
                from_dt = datetime.fromtimestamp(date_from.seconds)
            else:
                from_dt = date_from
            query = query & Q(**{f"{field_name}__gte": from_dt})
            logger.debug("Added date_from filter", field=field_name, dateFrom=from_dt)
        
        if date_to:
            # Convert timestamp to datetime if needed
            if hasattr(date_to, 'seconds'):
                to_dt = datetime.fromtimestamp(date_to.seconds)
            else:
                to_dt = date_to
            query = query & Q(**{f"{field_name}__lte": to_dt})
            logger.debug("Added date_to filter", field=field_name, dateTo=to_dt)
        
        return query

    @staticmethod
    def build_numeric_range_query(
        field_name: str,
        min_value: Optional[Union[int, float]] = None,
        max_value: Optional[Union[int, float]] = None
    ) -> Q:
        """
        Build a numeric range query for a specific field.

        Args:
            field_name: Name of the numeric field to filter on
            min_value: Minimum value (inclusive)
            max_value: Maximum value (inclusive)

        Returns:
            MongoDB Q query object
        """
        query = Q()
        
        if min_value is not None:
            query = query & Q(**{f"{field_name}__gte": min_value})
            logger.debug("Added min_value filter", field=field_name, minValue=min_value)
        
        if max_value is not None:
            query = query & Q(**{f"{field_name}__lte": max_value})
            logger.debug("Added max_value filter", field=field_name, maxValue=max_value)
        
        return query

    @staticmethod
    def build_multi_value_query(
        field_name: str,
        values: List[Any],
        match_any: bool = True
    ) -> Q:
        """
        Build a query to match multiple values in a field.

        Args:
            field_name: Name of the field to filter on
            values: List of values to match
            match_any: If True, match any value (OR). If False, match all values (AND)

        Returns:
            MongoDB Q query object
        """
        if not values:
            return Q()

        if match_any:
            # OR logic: field matches any of the values
            query = Q(**{f"{field_name}__in": values})
            logger.debug("Built multi-value OR query", field=field_name, values=values)
        else:
            # AND logic: field matches all values (useful for array fields)
            query = Q(**{f"{field_name}__all": values})
            logger.debug("Built multi-value AND query", field=field_name, values=values)
        
        return query

    @staticmethod
    def build_complex_search_query(
        base_query: Q,
        search_filters: Dict[str, Any]
    ) -> Q:
        """
        Build a complex search query by combining multiple filter types.

        Args:
            base_query: Base query to start with
            search_filters: Dictionary of search filters to apply

        Returns:
            Combined MongoDB Q query object
        """
        query = base_query

        # Text search filters
        if "text_search" in search_filters:
            text_config = search_filters["text_search"]
            text_query = AdvancedSearchBuilder.build_text_search_query(
                search_term=text_config.get("term", ""),
                fields=text_config.get("fields", []),
                case_sensitive=text_config.get("case_sensitive", False),
                exact_match=text_config.get("exact_match", False)
            )
            query = query & text_query

        # Date range filters
        if "date_ranges" in search_filters:
            for field_name, date_config in search_filters["date_ranges"].items():
                date_query = AdvancedSearchBuilder.build_date_range_query(
                    field_name=field_name,
                    date_from=date_config.get("from"),
                    date_to=date_config.get("to")
                )
                query = query & date_query

        # Numeric range filters
        if "numeric_ranges" in search_filters:
            for field_name, numeric_config in search_filters["numeric_ranges"].items():
                numeric_query = AdvancedSearchBuilder.build_numeric_range_query(
                    field_name=field_name,
                    min_value=numeric_config.get("min"),
                    max_value=numeric_config.get("max")
                )
                query = query & numeric_query

        # Multi-value filters
        if "multi_values" in search_filters:
            for field_name, multi_config in search_filters["multi_values"].items():
                multi_query = AdvancedSearchBuilder.build_multi_value_query(
                    field_name=field_name,
                    values=multi_config.get("values", []),
                    match_any=multi_config.get("match_any", True)
                )
                query = query & multi_query

        # Exact match filters
        if "exact_matches" in search_filters:
            for field_name, value in search_filters["exact_matches"].items():
                if value is not None:
                    query = query & Q(**{field_name: value})

        logger.info(
            "Built complex search query",
            filterTypes=list(search_filters.keys()),
            hasTextSearch="text_search" in search_filters,
            hasDateRanges="date_ranges" in search_filters,
            hasNumericRanges="numeric_ranges" in search_filters,
            hasMultiValues="multi_values" in search_filters,
            hasExactMatches="exact_matches" in search_filters
        )

        return query


class SearchResultOptimizer:
    """
    Utility class for optimizing and ranking search results.
    """

    @staticmethod
    def calculate_relevance_score(
        item: Any,
        search_term: str,
        score_fields: List[str],
        boost_factors: Optional[Dict[str, float]] = None
    ) -> float:
        """
        Calculate a relevance score for a search result item.

        Args:
            item: The search result item (document object)
            search_term: The original search term
            score_fields: List of field names to consider for scoring
            boost_factors: Optional dictionary of field boost factors

        Returns:
            Relevance score (higher is more relevant)
        """
        if not search_term or not search_term.strip():
            return 0.0

        search_term_lower = search_term.lower().strip()
        total_score = 0.0
        boost_factors = boost_factors or {}

        for field in score_fields:
            try:
                field_value = getattr(item, field, "")
                if field_value and isinstance(field_value, str):
                    field_value_lower = field_value.lower()
                    
                    # Calculate field score based on different match types
                    field_score = 0.0
                    
                    # Exact match (highest score)
                    if search_term_lower == field_value_lower:
                        field_score = 100.0
                    # Starts with search term
                    elif field_value_lower.startswith(search_term_lower):
                        field_score = 75.0
                    # Contains search term
                    elif search_term_lower in field_value_lower:
                        field_score = 50.0
                        # Bonus for multiple occurrences
                        occurrences = field_value_lower.count(search_term_lower)
                        field_score += min(occurrences * 10, 25)
                    
                    # Apply boost factor if specified
                    boost = boost_factors.get(field, 1.0)
                    field_score *= boost
                    
                    total_score += field_score
                    
            except Exception as e:
                logger.debug(f"Error calculating score for field {field}", error=str(e))
                continue

        return total_score

    @staticmethod
    def rank_search_results(
        results: List[Any],
        search_term: str,
        score_fields: List[str],
        boost_factors: Optional[Dict[str, float]] = None,
        max_results: Optional[int] = None
    ) -> List[Tuple[Any, float]]:
        """
        Rank search results by relevance score.

        Args:
            results: List of search result items
            search_term: The original search term
            score_fields: List of field names to consider for scoring
            boost_factors: Optional dictionary of field boost factors
            max_results: Maximum number of results to return

        Returns:
            List of tuples (item, score) sorted by relevance score (descending)
        """
        if not results:
            return []

        # Calculate scores for all results
        scored_results = []
        for item in results:
            score = SearchResultOptimizer.calculate_relevance_score(
                item, search_term, score_fields, boost_factors
            )
            scored_results.append((item, score))

        # Sort by score (descending)
        scored_results.sort(key=lambda x: x[1], reverse=True)

        # Limit results if specified
        if max_results and max_results > 0:
            scored_results = scored_results[:max_results]

        logger.info(
            "Ranked search results",
            totalResults=len(results),
            rankedResults=len(scored_results),
            searchTerm=search_term,
            scoreFields=score_fields,
            maxResults=max_results,
            topScore=scored_results[0][1] if scored_results else 0.0
        )

        return scored_results
