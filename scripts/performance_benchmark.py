#!/usr/bin/env python3
"""
Performance benchmark script for the enhanced Communication Service APIs.

This script measures and compares performance metrics before and after
the enhancements including response times, database query counts,
cache hit rates, and memory usage.
"""

import time
import statistics
import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any
import psutil
import os

# Import service components
from app.services.conversation_service import ConversationService
from app.services.message_service import MessageService
from app.utils.cache_manager import get_cache_manager
from app.utils.query_cache import QueryCache
from app.protos import communication_pb2


class PerformanceBenchmark:
    """Performance benchmark suite for Communication Service APIs."""

    def __init__(self):
        """Initialize benchmark suite."""
        self.conversation_service = ConversationService()
        self.message_service = MessageService()
        self.cache_manager = get_cache_manager()
        self.query_cache = QueryCache()
        
        # Benchmark results storage
        self.results = {
            "conversation_list": {},
            "message_list": {},
            "cache_performance": {},
            "search_performance": {},
            "system_metrics": {}
        }

    def measure_execution_time(self, func, *args, **kwargs) -> tuple:
        """
        Measure execution time of a function.

        Args:
            func: Function to measure
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Tuple of (result, execution_time_ms)
        """
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
        return result, execution_time

    def measure_memory_usage(self) -> Dict[str, float]:
        """
        Measure current memory usage.

        Returns:
            Dictionary containing memory metrics
        """
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,  # Resident Set Size in MB
            "vms_mb": memory_info.vms / 1024 / 1024,  # Virtual Memory Size in MB
            "percent": process.memory_percent()
        }

    def benchmark_conversation_list_basic(self, iterations: int = 100) -> Dict[str, Any]:
        """
        Benchmark basic conversation list performance.

        Args:
            iterations: Number of test iterations

        Returns:
            Performance metrics dictionary
        """
        print(f"Running conversation list benchmark ({iterations} iterations)...")
        
        # Create test request
        request = communication_pb2.ListConversationsRequest(
            userId="benchmark_user",
            limit=20,
            offset=0
        )
        
        # Mock context
        from unittest.mock import Mock
        context = Mock()
        
        execution_times = []
        memory_before = self.measure_memory_usage()
        
        for i in range(iterations):
            try:
                _, exec_time = self.measure_execution_time(
                    self.conversation_service.listConversations,
                    request,
                    context
                )
                execution_times.append(exec_time)
                
                if i % 10 == 0:
                    print(f"  Completed {i}/{iterations} iterations")
                    
            except Exception as e:
                print(f"  Error in iteration {i}: {str(e)}")
                continue
        
        memory_after = self.measure_memory_usage()
        
        if execution_times:
            metrics = {
                "iterations": len(execution_times),
                "avg_response_time_ms": statistics.mean(execution_times),
                "median_response_time_ms": statistics.median(execution_times),
                "min_response_time_ms": min(execution_times),
                "max_response_time_ms": max(execution_times),
                "std_dev_ms": statistics.stdev(execution_times) if len(execution_times) > 1 else 0,
                "memory_delta_mb": memory_after["rss_mb"] - memory_before["rss_mb"],
                "total_memory_mb": memory_after["rss_mb"]
            }
        else:
            metrics = {"error": "No successful iterations completed"}
        
        return metrics

    def benchmark_conversation_list_with_filters(self, iterations: int = 50) -> Dict[str, Any]:
        """
        Benchmark conversation list with advanced filters.

        Args:
            iterations: Number of test iterations

        Returns:
            Performance metrics dictionary
        """
        print(f"Running filtered conversation list benchmark ({iterations} iterations)...")
        
        # Create request with filters
        request = communication_pb2.ListConversationsRequest(
            userId="benchmark_user",
            chatType=1,
            limit=20,
            offset=0
        )
        
        # Add filter attributes
        request.dateFrom = (datetime.now() - timedelta(days=30)).isoformat()
        request.dateTo = datetime.now().isoformat()
        request.minInputTokens = 50
        request.maxInputTokens = 1000
        request.sortBy = "CREATED_AT"
        request.sortOrder = "DESC"
        
        from unittest.mock import Mock
        context = Mock()
        
        execution_times = []
        
        for i in range(iterations):
            try:
                _, exec_time = self.measure_execution_time(
                    self.conversation_service.listConversations,
                    request,
                    context
                )
                execution_times.append(exec_time)
                
            except Exception as e:
                print(f"  Error in iteration {i}: {str(e)}")
                continue
        
        if execution_times:
            return {
                "iterations": len(execution_times),
                "avg_response_time_ms": statistics.mean(execution_times),
                "median_response_time_ms": statistics.median(execution_times),
                "min_response_time_ms": min(execution_times),
                "max_response_time_ms": max(execution_times)
            }
        else:
            return {"error": "No successful iterations completed"}

    def benchmark_search_performance(self, iterations: int = 30) -> Dict[str, Any]:
        """
        Benchmark search functionality performance.

        Args:
            iterations: Number of test iterations

        Returns:
            Performance metrics dictionary
        """
        print(f"Running search performance benchmark ({iterations} iterations)...")
        
        search_queries = [
            "project discussion",
            "code review AND analysis",
            "meeting OR planning",
            "bug fix NOT resolved",
            '"exact phrase search"'
        ]
        
        results = {}
        
        for query in search_queries:
            request = communication_pb2.ListConversationsRequest(
                userId="benchmark_user",
                limit=10,
                offset=0
            )
            request.search = query
            
            from unittest.mock import Mock
            context = Mock()
            
            execution_times = []
            
            for i in range(iterations):
                try:
                    _, exec_time = self.measure_execution_time(
                        self.conversation_service.listConversations,
                        request,
                        context
                    )
                    execution_times.append(exec_time)
                    
                except Exception as e:
                    continue
            
            if execution_times:
                results[query] = {
                    "avg_response_time_ms": statistics.mean(execution_times),
                    "median_response_time_ms": statistics.median(execution_times),
                    "iterations": len(execution_times)
                }
        
        return results

    def benchmark_cache_performance(self, iterations: int = 100) -> Dict[str, Any]:
        """
        Benchmark cache performance and hit rates.

        Args:
            iterations: Number of test iterations

        Returns:
            Cache performance metrics
        """
        print(f"Running cache performance benchmark ({iterations} iterations)...")
        
        # Clear cache first
        self.cache_manager.delete_pattern("*")
        
        # Test data
        test_data = {"test": "data", "timestamp": datetime.now().isoformat()}
        
        # Benchmark cache operations
        set_times = []
        get_times = []
        
        # Test cache set performance
        for i in range(iterations):
            key = f"benchmark_key_{i}"
            _, exec_time = self.measure_execution_time(
                self.cache_manager.set,
                key,
                test_data
            )
            set_times.append(exec_time)
        
        # Test cache get performance (should be cache hits)
        for i in range(iterations):
            key = f"benchmark_key_{i}"
            _, exec_time = self.measure_execution_time(
                self.cache_manager.get,
                key
            )
            get_times.append(exec_time)
        
        # Get cache statistics
        cache_stats = self.cache_manager.get_cache_stats()
        
        return {
            "set_operations": {
                "avg_time_ms": statistics.mean(set_times),
                "median_time_ms": statistics.median(set_times),
                "iterations": len(set_times)
            },
            "get_operations": {
                "avg_time_ms": statistics.mean(get_times),
                "median_time_ms": statistics.median(get_times),
                "iterations": len(get_times)
            },
            "cache_stats": cache_stats
        }

    def benchmark_message_list_performance(self, iterations: int = 50) -> Dict[str, Any]:
        """
        Benchmark message list API performance.

        Args:
            iterations: Number of test iterations

        Returns:
            Performance metrics dictionary
        """
        print(f"Running message list benchmark ({iterations} iterations)...")
        
        # Create test request
        request = communication_pb2.ListMessagesRequest(
            conversationId="benchmark_conv",
            userId="benchmark_user",
            limit=50,
            offset=0
        )
        
        from unittest.mock import Mock
        context = Mock()
        
        execution_times = []
        
        for i in range(iterations):
            try:
                _, exec_time = self.measure_execution_time(
                    self.message_service.listMessages,
                    request,
                    context
                )
                execution_times.append(exec_time)
                
            except Exception as e:
                continue
        
        if execution_times:
            return {
                "iterations": len(execution_times),
                "avg_response_time_ms": statistics.mean(execution_times),
                "median_response_time_ms": statistics.median(execution_times),
                "min_response_time_ms": min(execution_times),
                "max_response_time_ms": max(execution_times)
            }
        else:
            return {"error": "No successful iterations completed"}

    def run_full_benchmark_suite(self) -> Dict[str, Any]:
        """
        Run the complete benchmark suite.

        Returns:
            Complete benchmark results
        """
        print("Starting Performance Benchmark Suite")
        print("=" * 50)
        
        start_time = datetime.now()
        
        # Run individual benchmarks
        self.results["conversation_list"]["basic"] = self.benchmark_conversation_list_basic()
        self.results["conversation_list"]["filtered"] = self.benchmark_conversation_list_with_filters()
        self.results["message_list"]["basic"] = self.benchmark_message_list_performance()
        self.results["search_performance"] = self.benchmark_search_performance()
        self.results["cache_performance"] = self.benchmark_cache_performance()
        
        # System metrics
        self.results["system_metrics"] = {
            "cpu_count": psutil.cpu_count(),
            "memory_total_gb": psutil.virtual_memory().total / 1024 / 1024 / 1024,
            "benchmark_duration_seconds": (datetime.now() - start_time).total_seconds(),
            "timestamp": datetime.now().isoformat()
        }
        
        return self.results

    def generate_report(self, results: Dict[str, Any]) -> str:
        """
        Generate a formatted benchmark report.

        Args:
            results: Benchmark results dictionary

        Returns:
            Formatted report string
        """
        report = []
        report.append("Communication Service Performance Benchmark Report")
        report.append("=" * 60)
        report.append(f"Generated: {results['system_metrics']['timestamp']}")
        report.append(f"Duration: {results['system_metrics']['benchmark_duration_seconds']:.2f} seconds")
        report.append("")
        
        # Conversation List Performance
        report.append("Conversation List API Performance:")
        report.append("-" * 40)
        
        basic = results["conversation_list"]["basic"]
        if "error" not in basic:
            report.append(f"Basic List (avg): {basic['avg_response_time_ms']:.2f}ms")
            report.append(f"Basic List (median): {basic['median_response_time_ms']:.2f}ms")
            report.append(f"Memory usage: {basic['memory_delta_mb']:.2f}MB delta")
        
        filtered = results["conversation_list"]["filtered"]
        if "error" not in filtered:
            report.append(f"Filtered List (avg): {filtered['avg_response_time_ms']:.2f}ms")
            report.append(f"Filtered List (median): {filtered['median_response_time_ms']:.2f}ms")
        
        report.append("")
        
        # Message List Performance
        report.append("Message List API Performance:")
        report.append("-" * 40)
        
        msg_basic = results["message_list"]["basic"]
        if "error" not in msg_basic:
            report.append(f"Basic List (avg): {msg_basic['avg_response_time_ms']:.2f}ms")
            report.append(f"Basic List (median): {msg_basic['median_response_time_ms']:.2f}ms")
        
        report.append("")
        
        # Search Performance
        report.append("Search Performance:")
        report.append("-" * 40)
        
        for query, metrics in results["search_performance"].items():
            report.append(f"'{query}': {metrics['avg_response_time_ms']:.2f}ms avg")
        
        report.append("")
        
        # Cache Performance
        report.append("Cache Performance:")
        report.append("-" * 40)
        
        cache_perf = results["cache_performance"]
        report.append(f"Cache Set (avg): {cache_perf['set_operations']['avg_time_ms']:.2f}ms")
        report.append(f"Cache Get (avg): {cache_perf['get_operations']['avg_time_ms']:.2f}ms")
        
        if "cache_stats" in cache_perf and "hit_rate" in cache_perf["cache_stats"]:
            report.append(f"Cache Hit Rate: {cache_perf['cache_stats']['hit_rate']:.1f}%")
        
        report.append("")
        
        # System Information
        report.append("System Information:")
        report.append("-" * 40)
        report.append(f"CPU Cores: {results['system_metrics']['cpu_count']}")
        report.append(f"Total Memory: {results['system_metrics']['memory_total_gb']:.2f}GB")
        
        return "\n".join(report)


def main():
    """Main benchmark execution function."""
    benchmark = PerformanceBenchmark()
    
    try:
        # Run benchmark suite
        results = benchmark.run_full_benchmark_suite()
        
        # Generate and save report
        report = benchmark.generate_report(results)
        
        # Print report to console
        print("\n" + report)
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save JSON results
        with open(f"benchmark_results_{timestamp}.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        # Save text report
        with open(f"benchmark_report_{timestamp}.txt", "w") as f:
            f.write(report)
        
        print(f"\nResults saved to benchmark_results_{timestamp}.json")
        print(f"Report saved to benchmark_report_{timestamp}.txt")
        
    except Exception as e:
        print(f"Benchmark failed: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
