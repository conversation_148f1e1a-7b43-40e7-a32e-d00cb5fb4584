"""
Unit tests for MongoDB connection and optimization features.

This module tests the database connection pooling, timeout configurations,
and index creation functionality.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import os

from app.db.mongo import init_db, init_mongo_from_env, close_db, create_indexes


class TestMongoConnection(unittest.TestCase):
    """Test cases for MongoDB connection functionality."""

    @patch('app.db.mongo.connect')
    @patch('app.db.mongo.get_db')
    def test_init_db_without_auth(self, mock_get_db, mock_connect):
        """Test database initialization without authentication."""
        init_db(
            host="localhost",
            port=27017,
            database="test_db"
        )
        
        # Verify connection was called with optimization parameters
        mock_connect.assert_called_once_with(
            db="test_db",
            host="localhost",
            port=27017,
            maxPoolSize=50,
            minPoolSize=5,
            maxIdleTimeMS=30000,
            serverSelectionTimeoutMS=5000,
            socketTimeoutMS=20000,
            connectTimeoutMS=10000
        )
        
        # Verify database connection was tested
        mock_get_db.assert_called_once()

    @patch('app.db.mongo.connect')
    @patch('app.db.mongo.get_db')
    def test_init_db_with_auth_standard(self, mock_get_db, mock_connect):
        """Test database initialization with authentication for standard MongoDB."""
        init_db(
            host="localhost",
            port=27017,
            database="test_db",
            username="testuser",
            password="testpass"
        )
        
        # Verify connection was called with URI including auth and optimizations
        expected_uri = (
            "***************************************************"
            "?authSource=admin&maxPoolSize=50&minPoolSize=5"
            "&maxIdleTimeMS=30000&serverSelectionTimeoutMS=5000"
            "&socketTimeoutMS=20000&connectTimeoutMS=10000"
        )
        mock_connect.assert_called_once_with(host=expected_uri)
        mock_get_db.assert_called_once()

    @patch('app.db.mongo.connect')
    @patch('app.db.mongo.get_db')
    def test_init_db_with_auth_atlas(self, mock_get_db, mock_connect):
        """Test database initialization with MongoDB Atlas."""
        init_db(
            host="cluster0.mongodb.net",
            port=27017,
            database="test_db",
            username="testuser",
            password="testpass"
        )
        
        # Verify connection was called with Atlas URI and optimizations
        expected_uri = (
            "mongodb+srv://testuser:<EMAIL>/test_db"
            "?retryWrites=true&w=majority&maxPoolSize=50&minPoolSize=5"
            "&maxIdleTimeMS=30000&serverSelectionTimeoutMS=5000"
            "&socketTimeoutMS=20000&connectTimeoutMS=10000"
        )
        mock_connect.assert_called_once_with(host=expected_uri)
        mock_get_db.assert_called_once()

    @patch('app.db.mongo.connect')
    def test_init_db_connection_error(self, mock_connect):
        """Test database initialization with connection error."""
        mock_connect.side_effect = Exception("Connection failed")
        
        with self.assertRaises(ConnectionError) as context:
            init_db(
                host="localhost",
                port=27017,
                database="test_db"
            )
        
        self.assertIn("Could not connect to MongoDB", str(context.exception))

    @patch('app.db.mongo.init_db')
    @patch('app.db.mongo.settings')
    def test_init_mongo_from_env(self, mock_settings, mock_init_db):
        """Test MongoDB initialization from environment variables."""
        # Mock settings
        mock_settings.MONGO_HOST = "localhost"
        mock_settings.MONGO_PORT = 27017
        mock_settings.MONGO_DB_NAME = "test_db"
        mock_settings.MONGO_USERNAME = "testuser"
        mock_settings.MONGO_PASSWORD = "testpass"
        
        init_mongo_from_env()
        
        # Verify init_db was called with correct parameters
        mock_init_db.assert_called_once_with(
            host="localhost",
            port=27017,
            database="test_db",
            username="testuser",
            password="testpass"
        )

    @patch('app.db.mongo.init_db')
    @patch('app.db.mongo.settings')
    def test_init_mongo_from_env_error(self, mock_settings, mock_init_db):
        """Test MongoDB initialization error handling."""
        mock_settings.MONGO_HOST = "localhost"
        mock_settings.MONGO_PORT = 27017
        mock_settings.MONGO_DB_NAME = "test_db"
        mock_settings.MONGO_USERNAME = None
        mock_settings.MONGO_PASSWORD = None
        
        mock_init_db.side_effect = Exception("Connection failed")
        
        with self.assertRaises(RuntimeError) as context:
            init_mongo_from_env()
        
        self.assertIn("Could not connect to MongoDB", str(context.exception))

    @patch('app.db.mongo.disconnect')
    def test_close_db_success(self, mock_disconnect):
        """Test successful database connection closure."""
        close_db()
        mock_disconnect.assert_called_once()

    @patch('app.db.mongo.disconnect')
    def test_close_db_error(self, mock_disconnect):
        """Test database closure with error."""
        mock_disconnect.side_effect = Exception("Disconnect failed")
        
        with self.assertRaises(ConnectionError) as context:
            close_db()
        
        self.assertIn("Could not close MongoDB connections", str(context.exception))


class TestIndexCreation(unittest.TestCase):
    """Test cases for database index creation."""

    @patch('app.models.task_model.Task')
    @patch('app.models.message_model.Message')
    @patch('app.models.conversation_model.Conversation')
    def test_create_indexes_success(self, mock_conversation, mock_message, mock_task):
        """Test successful index creation."""
        create_indexes()
        
        # Verify ensure_indexes was called for all models
        mock_conversation.ensure_indexes.assert_called_once()
        mock_message.ensure_indexes.assert_called_once()
        mock_task.ensure_indexes.assert_called_once()

    @patch('app.models.task_model.Task')
    @patch('app.models.message_model.Message')
    @patch('app.models.conversation_model.Conversation')
    def test_create_indexes_error(self, mock_conversation, mock_message, mock_task):
        """Test index creation with error (should not raise)."""
        mock_conversation.ensure_indexes.side_effect = Exception("Index creation failed")
        
        # Should not raise an exception
        try:
            create_indexes()
        except Exception:
            self.fail("create_indexes should not raise exceptions")


class TestConnectionPooling(unittest.TestCase):
    """Test cases for connection pooling optimizations."""

    @patch('app.db.mongo.connect')
    @patch('app.db.mongo.get_db')
    def test_connection_pool_parameters(self, mock_get_db, mock_connect):
        """Test that connection pooling parameters are correctly set."""
        init_db(
            host="localhost",
            port=27017,
            database="test_db"
        )
        
        # Extract the call arguments
        call_args = mock_connect.call_args
        kwargs = call_args[1] if call_args else {}
        
        # Verify pooling parameters
        self.assertEqual(kwargs.get('maxPoolSize'), 50)
        self.assertEqual(kwargs.get('minPoolSize'), 5)
        self.assertEqual(kwargs.get('maxIdleTimeMS'), 30000)
        self.assertEqual(kwargs.get('serverSelectionTimeoutMS'), 5000)
        self.assertEqual(kwargs.get('socketTimeoutMS'), 20000)
        self.assertEqual(kwargs.get('connectTimeoutMS'), 10000)

    @patch('app.db.mongo.connect')
    @patch('app.db.mongo.get_db')
    def test_atlas_connection_optimizations(self, mock_get_db, mock_connect):
        """Test Atlas-specific connection optimizations."""
        init_db(
            host="cluster0.mongodb.net",
            port=27017,
            database="test_db",
            username="user",
            password="pass"
        )
        
        # Verify Atlas URI contains optimization parameters
        call_args = mock_connect.call_args
        uri = call_args[1]['host'] if call_args and call_args[1] else ""
        
        self.assertIn("retryWrites=true", uri)
        self.assertIn("w=majority", uri)
        self.assertIn("maxPoolSize=50", uri)
        self.assertIn("minPoolSize=5", uri)
        self.assertIn("maxIdleTimeMS=30000", uri)
        self.assertIn("serverSelectionTimeoutMS=5000", uri)


class TestPerformanceOptimizations(unittest.TestCase):
    """Test cases for performance optimization features."""

    def test_timeout_configurations(self):
        """Test that timeout configurations are properly set."""
        # This test verifies the timeout values are reasonable
        timeouts = {
            'serverSelectionTimeoutMS': 5000,
            'socketTimeoutMS': 20000,
            'connectTimeoutMS': 10000,
            'maxIdleTimeMS': 30000
        }
        
        for timeout_name, timeout_value in timeouts.items():
            self.assertIsInstance(timeout_value, int)
            self.assertGreater(timeout_value, 0)
            self.assertLessEqual(timeout_value, 60000)  # Max 60 seconds

    def test_pool_size_configurations(self):
        """Test that pool size configurations are optimal."""
        max_pool_size = 50
        min_pool_size = 5
        
        self.assertGreater(max_pool_size, min_pool_size)
        self.assertGreaterEqual(min_pool_size, 1)
        self.assertLessEqual(max_pool_size, 100)  # Reasonable upper limit

    @patch('app.db.mongo.connect')
    @patch('app.db.mongo.get_db')
    def test_retry_configurations(self, mock_get_db, mock_connect):
        """Test retry configurations for Atlas connections."""
        init_db(
            host="cluster0.mongodb.net",
            port=27017,
            database="test_db",
            username="user",
            password="pass"
        )
        
        call_args = mock_connect.call_args
        uri = call_args[1]['host'] if call_args and call_args[1] else ""
        
        # Verify retry configurations
        self.assertIn("retryWrites=true", uri)
        self.assertIn("w=majority", uri)


if __name__ == '__main__':
    unittest.main()