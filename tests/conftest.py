"""
Pytest configuration and fixtures for the communication service tests.

This module provides common test fixtures and configuration for all test modules.
"""

import pytest
from unittest.mock import Mock, patch
import sys
import os

# Add the app directory to the Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


@pytest.fixture
def mock_grpc_context():
    """Fixture providing a mock gRPC servicer context."""
    context = Mock()
    context.abort = Mock()
    return context


@pytest.fixture
def mock_conversation_request():
    """Fixture providing a mock conversation request."""
    request = Mock()
    request.userId = "test_user_123"
    request.agentId = "test_agent_456"
    request.chatType = 1
    return request


@pytest.fixture
def mock_conversation_model():
    """Fixture providing a mock conversation model."""
    conversation = Mock()
    conversation.id = "test_conversation_id"
    conversation.userId = "test_user_123"
    conversation.agentId = "test_agent_456"
    conversation.chatType = "CHAT_TYPE_GLOBAL"
    conversation.inputTokens = 100
    conversation.outputTokens = 50
    conversation.save = Mock()
    conversation.delete = Mock()
    conversation.to_proto = Mock(return_value="mock_proto_conversation")
    return conversation


@pytest.fixture
def mock_database_connection():
    """Fixture providing a mock database connection."""
    with patch('app.db.mongo.connect') as mock_connect, \
         patch('app.db.mongo.get_db') as mock_get_db:
        yield mock_connect, mock_get_db


@pytest.fixture
def mock_logger():
    """Fixture providing a mock logger."""
    with patch('app.utils.logger.setup_logger') as mock_setup_logger:
        mock_logger = Mock()
        mock_setup_logger.return_value = mock_logger
        yield mock_logger


@pytest.fixture(autouse=True)
def mock_environment_variables():
    """Fixture that mocks environment variables for all tests."""
    env_vars = {
        'MONGO_HOST': 'localhost',
        'MONGO_PORT': '27017',
        'MONGO_DB_NAME': 'test_communication_service',
        'MONGO_USERNAME': 'test_user',
        'MONGO_PASSWORD': 'test_password',
        'GRPC_PORT': '50055'
    }
    
    with patch.dict(os.environ, env_vars):
        yield


@pytest.fixture
def mock_settings():
    """Fixture providing mock settings."""
    with patch('app.core.config.settings') as mock_settings:
        mock_settings.MONGO_HOST = 'localhost'
        mock_settings.MONGO_PORT = 27017
        mock_settings.MONGO_DB_NAME = 'test_communication_service'
        mock_settings.MONGO_USERNAME = 'test_user'
        mock_settings.MONGO_PASSWORD = 'test_password'
        mock_settings.GRPC_PORT = '50055'
        yield mock_settings


# Test configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )


# Test collection configuration
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add unit test marker to all tests by default
        if not any(marker.name in ['integration', 'performance'] 
                  for marker in item.iter_markers()):
            item.add_marker(pytest.mark.unit)