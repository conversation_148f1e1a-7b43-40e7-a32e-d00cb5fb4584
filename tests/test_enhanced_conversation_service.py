"""
Comprehensive tests for enhanced conversation service functionality.

This test suite covers all the new features and improvements including:
- Advanced filtering and pagination
- Response optimization
- Caching functionality
- Search capabilities
- Performance optimizations
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import json

# Import the service and related modules
from app.services.conversation_service import ConversationService
from app.utils.cache_manager import CacheManager
from app.utils.query_cache import QueryCache
from app.utils.response_optimizer import ResponseOptimizer
from app.utils.advanced_search import AdvancedSearchBuilder, SearchResultOptimizer

# Import protobuf messages
from app.protos import communication_pb2


class TestEnhancedConversationService(unittest.TestCase):
    """Test suite for enhanced conversation service functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.service = ConversationService()
        self.mock_context = Mock()
        
        # Create sample conversation data
        self.sample_conversations = [
            {
                "id": "conv1",
                "userId": "user123",
                "agentId": "agent1",
                "chatType": 1,  # CHAT_TYPE_GLOBAL
                "title": "Project Discussion",
                "inputTokens": 150,
                "outputTokens": 300,
                "createdAt": datetime.now() - timedelta(days=1),
                "updatedAt": datetime.now() - timedelta(hours=2),
            },
            {
                "id": "conv2", 
                "userId": "user123",
                "agentId": "agent2",
                "chatType": 2,  # CHAT_TYPE_PRIVATE
                "title": "Code Review",
                "inputTokens": 200,
                "outputTokens": 400,
                "createdAt": datetime.now() - timedelta(days=2),
                "updatedAt": datetime.now() - timedelta(hours=1),
            }
        ]

    def test_advanced_filtering_date_range(self):
        """Test date range filtering functionality."""
        # Create request with date range
        request = communication_pb2.ListConversationsRequest(
            userId="user123",
            limit=10,
            offset=0
        )
        
        # Add date range attributes
        date_from = datetime.now() - timedelta(days=3)
        date_to = datetime.now()
        request.dateFrom = date_from.isoformat()
        request.dateTo = date_to.isoformat()
        
        with patch('app.models.conversation_model.Conversation') as mock_conversation:
            # Mock the database query
            mock_query = Mock()
            mock_conversation.objects.filter.return_value = mock_query
            mock_query.count.return_value = 2
            mock_query.skip.return_value = mock_query
            mock_query.limit.return_value = self.sample_conversations
            
            # Call the service
            response = self.service.listConversations(request, self.mock_context)
            
            # Verify response
            self.assertIsInstance(response, communication_pb2.ListConversationsResponse)
            self.assertEqual(len(response.data), 2)
            self.assertEqual(response.metadata.total, 2)

    def test_token_usage_filtering(self):
        """Test filtering by token usage ranges."""
        request = communication_pb2.ListConversationsRequest(
            userId="user123",
            limit=10,
            offset=0
        )
        
        # Add token filtering attributes
        request.minInputTokens = 100
        request.maxInputTokens = 500
        request.minOutputTokens = 200
        request.maxOutputTokens = 600
        
        with patch('app.models.conversation_model.Conversation') as mock_conversation:
            mock_query = Mock()
            mock_conversation.objects.filter.return_value = mock_query
            mock_query.count.return_value = 1
            mock_query.skip.return_value = mock_query
            mock_query.limit.return_value = [self.sample_conversations[0]]
            
            response = self.service.listConversations(request, self.mock_context)
            
            self.assertEqual(len(response.data), 1)
            self.assertEqual(response.metadata.total, 1)

    def test_advanced_search_functionality(self):
        """Test advanced search with relevance ranking."""
        request = communication_pb2.ListConversationsRequest(
            userId="user123",
            limit=10,
            offset=0
        )
        
        # Add search query
        request.search = "project AND discussion"
        
        with patch('app.models.conversation_model.Conversation') as mock_conversation:
            with patch('app.utils.advanced_search.AdvancedSearchBuilder') as mock_search_builder:
                with patch('app.utils.advanced_search.SearchResultOptimizer') as mock_optimizer:
                    # Mock search functionality
                    mock_query = Mock()
                    mock_conversation.objects.filter.return_value = mock_query
                    mock_query.count.return_value = 1
                    mock_query.skip.return_value = mock_query
                    mock_query.limit.return_value = [self.sample_conversations[0]]
                    
                    # Mock search builder
                    mock_search_builder.build_complex_search_query.return_value = Mock()
                    
                    # Mock search result optimizer
                    mock_optimizer.rank_search_results.return_value = [
                        (self.sample_conversations[0], 0.95)
                    ]
                    
                    response = self.service.listConversations(request, self.mock_context)
                    
                    self.assertEqual(len(response.data), 1)
                    mock_search_builder.build_complex_search_query.assert_called_once()
                    mock_optimizer.rank_search_results.assert_called_once()

    def test_sorting_functionality(self):
        """Test various sorting options."""
        request = communication_pb2.ListConversationsRequest(
            userId="user123",
            limit=10,
            offset=0
        )
        
        # Test sorting by update date descending
        request.sortBy = "UPDATED_AT"
        request.sortOrder = "DESC"
        
        with patch('app.models.conversation_model.Conversation') as mock_conversation:
            mock_query = Mock()
            mock_conversation.objects.filter.return_value = mock_query
            mock_query.count.return_value = 2
            mock_query.order_by.return_value = mock_query
            mock_query.skip.return_value = mock_query
            mock_query.limit.return_value = self.sample_conversations
            
            response = self.service.listConversations(request, self.mock_context)
            
            # Verify sorting was applied
            mock_query.order_by.assert_called()
            self.assertEqual(len(response.data), 2)

    def test_response_optimization(self):
        """Test response optimization features."""
        request = communication_pb2.ListConversationsRequest(
            userId="user123",
            limit=10,
            offset=0
        )
        
        # Enable response optimization
        request.optimizeResponse = True
        request.excludeFields.extend(["inputTokens", "outputTokens"])
        
        with patch('app.models.conversation_model.Conversation') as mock_conversation:
            with patch('app.utils.response_optimizer.ResponseOptimizer') as mock_optimizer:
                mock_query = Mock()
                mock_conversation.objects.filter.return_value = mock_query
                mock_query.count.return_value = 1
                mock_query.skip.return_value = mock_query
                mock_query.limit.return_value = [self.sample_conversations[0]]
                
                # Mock response optimizer
                mock_optimizer.optimize_list_response.return_value = [{"id": "conv1", "title": "Project Discussion"}]
                mock_optimizer.monitor_response_size.return_value = None
                
                response = self.service.listConversations(request, self.mock_context)
                
                # Verify optimization was called
                mock_optimizer.optimize_list_response.assert_called_once()
                mock_optimizer.monitor_response_size.assert_called_once()

    def test_caching_functionality(self):
        """Test query result caching."""
        request = communication_pb2.ListConversationsRequest(
            userId="user123",
            limit=10,
            offset=0
        )
        
        with patch('app.utils.query_cache.QueryCache') as mock_query_cache:
            # Mock cache miss first, then cache hit
            cache_instance = mock_query_cache.return_value
            cache_instance.get_cached_query_result.return_value = None
            cache_instance.cache_query_result.return_value = True
            
            with patch('app.models.conversation_model.Conversation') as mock_conversation:
                mock_query = Mock()
                mock_conversation.objects.filter.return_value = mock_query
                mock_query.count.return_value = 1
                mock_query.skip.return_value = mock_query
                mock_query.limit.return_value = [self.sample_conversations[0]]
                
                # First call - cache miss
                response1 = self.service.listConversations(request, self.mock_context)
                
                # Verify cache was checked and result was cached
                cache_instance.get_cached_query_result.assert_called_once()
                cache_instance.cache_query_result.assert_called_once()
                
                # Mock cache hit for second call
                cache_instance.get_cached_query_result.return_value = (
                    [{"id": "conv1", "title": "Project Discussion"}],
                    {"total": 1, "totalPages": 1, "currentPage": 1, "pageSize": 10, "hasNextPage": False, "hasPreviousPage": False}
                )
                
                # Second call - cache hit
                response2 = self.service.listConversations(request, self.mock_context)
                
                # Verify cached response was returned
                self.assertEqual(len(response2.data), 1)

    def test_pagination_metadata(self):
        """Test enhanced pagination metadata."""
        request = communication_pb2.ListConversationsRequest(
            userId="user123",
            limit=1,
            offset=0
        )
        
        with patch('app.models.conversation_model.Conversation') as mock_conversation:
            mock_query = Mock()
            mock_conversation.objects.filter.return_value = mock_query
            mock_query.count.return_value = 2
            mock_query.skip.return_value = mock_query
            mock_query.limit.return_value = [self.sample_conversations[0]]
            
            response = self.service.listConversations(request, self.mock_context)
            
            # Verify pagination metadata
            self.assertEqual(response.metadata.total, 2)
            self.assertEqual(response.metadata.totalPages, 2)
            self.assertEqual(response.metadata.currentPage, 1)
            self.assertEqual(response.metadata.pageSize, 1)
            self.assertTrue(response.metadata.hasNextPage)
            self.assertFalse(response.metadata.hasPreviousPage)

    def test_task_loading_optimization(self):
        """Test conditional task loading."""
        request = communication_pb2.ListConversationsRequest(
            userId="user123",
            limit=10,
            offset=0
        )
        
        # Test with tasks included
        request.includeTasks = True
        
        with patch('app.models.conversation_model.Conversation') as mock_conversation:
            with patch('app.utils.task_utils.batch_load_tasks') as mock_batch_load:
                mock_query = Mock()
                mock_conversation.objects.filter.return_value = mock_query
                mock_query.count.return_value = 1
                mock_query.skip.return_value = mock_query
                mock_query.limit.return_value = [self.sample_conversations[0]]
                
                # Mock task loading
                mock_batch_load.return_value = {"conv1": [{"id": "task1", "title": "Test Task"}]}
                
                response = self.service.listConversations(request, self.mock_context)
                
                # Verify task loading was called
                mock_batch_load.assert_called_once()

    def test_error_handling(self):
        """Test error handling and logging."""
        request = communication_pb2.ListConversationsRequest(
            userId="",  # Invalid empty user ID
            limit=10,
            offset=0
        )
        
        # Should abort with INVALID_ARGUMENT
        with self.assertRaises(Exception):
            self.service.listConversations(request, self.mock_context)
        
        # Verify context.abort was called
        self.mock_context.abort.assert_called()

    def test_performance_limits(self):
        """Test performance protection limits."""
        request = communication_pb2.ListConversationsRequest(
            userId="user123",
            limit=150,  # Exceeds maximum limit of 100
            offset=0
        )
        
        with patch('app.models.conversation_model.Conversation') as mock_conversation:
            mock_query = Mock()
            mock_conversation.objects.filter.return_value = mock_query
            mock_query.count.return_value = 50
            mock_query.skip.return_value = mock_query
            mock_query.limit.return_value = self.sample_conversations
            
            response = self.service.listConversations(request, self.mock_context)
            
            # Verify limit was capped at maximum
            self.assertLessEqual(response.metadata.pageSize, 100)


class TestCacheManager(unittest.TestCase):
    """Test suite for cache manager functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.cache_manager = CacheManager()

    @patch('redis.Redis')
    def test_cache_set_and_get(self, mock_redis):
        """Test basic cache set and get operations."""
        # Mock Redis client
        mock_client = Mock()
        mock_redis.return_value = mock_client
        mock_client.ping.return_value = True
        mock_client.setex.return_value = True
        mock_client.get.return_value = b'{"test": "value"}'
        
        # Test set operation
        result = self.cache_manager.set("test_key", {"test": "value"})
        self.assertTrue(result)
        
        # Test get operation
        value = self.cache_manager.get("test_key")
        self.assertEqual(value, {"test": "value"})

    @patch('redis.Redis')
    def test_cache_invalidation(self, mock_redis):
        """Test cache invalidation patterns."""
        mock_client = Mock()
        mock_redis.return_value = mock_client
        mock_client.ping.return_value = True
        mock_client.keys.return_value = [b'comm_service:query:conversations:user123:hash1']
        mock_client.delete.return_value = 1
        
        # Test pattern-based deletion
        deleted_count = self.cache_manager.delete_pattern("query:conversations:user123:*")
        self.assertEqual(deleted_count, 1)


class TestQueryCache(unittest.TestCase):
    """Test suite for query cache functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.query_cache = QueryCache()

    def test_cache_key_generation(self):
        """Test cache key generation consistency."""
        # Test that same parameters generate same key
        key1 = self.query_cache._generate_cache_key(
            "conversations", "user123", {"chatType": 1}, {"sortBy": "CREATED_AT"}, {"limit": 10}
        )
        key2 = self.query_cache._generate_cache_key(
            "conversations", "user123", {"chatType": 1}, {"sortBy": "CREATED_AT"}, {"limit": 10}
        )
        
        self.assertEqual(key1, key2)
        
        # Test that different parameters generate different keys
        key3 = self.query_cache._generate_cache_key(
            "conversations", "user123", {"chatType": 2}, {"sortBy": "CREATED_AT"}, {"limit": 10}
        )
        
        self.assertNotEqual(key1, key3)

    @patch('app.utils.cache_manager.get_cache_manager')
    def test_query_result_caching(self, mock_get_cache_manager):
        """Test query result caching and retrieval."""
        # Mock cache manager
        mock_cache_manager = Mock()
        mock_get_cache_manager.return_value = mock_cache_manager
        
        # Test cache miss
        mock_cache_manager.get.return_value = None
        result = self.query_cache.get_cached_query_result(
            "conversations", "user123", {"chatType": 1}
        )
        self.assertIsNone(result)
        
        # Test cache hit
        cached_data = ([{"id": "conv1"}], {"total": 1})
        mock_cache_manager.get.return_value = cached_data
        result = self.query_cache.get_cached_query_result(
            "conversations", "user123", {"chatType": 1}
        )
        self.assertEqual(result, cached_data)


if __name__ == '__main__':
    unittest.main()
