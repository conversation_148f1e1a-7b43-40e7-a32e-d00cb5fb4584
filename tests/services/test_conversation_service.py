"""
Unit tests for the optimized conversation service.

This module contains comprehensive tests for the conversation service
including timeout handling, error management, and performance optimizations.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import grpc
from google.protobuf import empty_pb2
import signal
import time

# Import the service and models
from app.services.conversation_service import (
    ConversationService, 
    timeout_handler, 
    batch_load_tasks
)
from app.grpc_ import communication_pb2
from app.models.conversation_model import Conversation
from app.models.message_model import Message
from app.models.task_model import Task


class TestTimeoutHandler(unittest.TestCase):
    """Test cases for the timeout handler decorator."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_context = Mock(spec=grpc.ServicerContext)
        self.mock_request = Mock()

    def test_timeout_handler_success(self):
        """Test timeout handler with successful operation."""
        @timeout_handler(timeout_seconds=5)
        def test_method(self, request, context):
            return "success"

        service = ConversationService()
        result = test_method(service, self.mock_request, self.mock_context)
        self.assertEqual(result, "success")

    def test_timeout_handler_timeout_error(self):
        """Test timeout handler with timeout error."""
        @timeout_handler(timeout_seconds=1)
        def slow_method(self, request, context):
            time.sleep(2)  # Simulate slow operation
            return "success"

        service = ConversationService()
        
        # Mock the timeout to trigger immediately
        with patch('signal.alarm') as mock_alarm, \
             patch('signal.signal') as mock_signal:
            
            # Simulate timeout by raising TimeoutError
            def side_effect(*args):
                raise TimeoutError("Operation timed out after 1 seconds")
            
            mock_alarm.side_effect = side_effect
            
            slow_method(service, self.mock_request, self.mock_context)
            
            # Verify context.abort was called with DEADLINE_EXCEEDED
            self.mock_context.abort.assert_called_once_with(
                grpc.StatusCode.DEADLINE_EXCEEDED, 
                "Operation timed out after 1 seconds"
            )

    def test_timeout_handler_database_error(self):
        """Test timeout handler with database error."""
        from pymongo.errors import PyMongoError
        
        @timeout_handler(timeout_seconds=5)
        def db_error_method(self, request, context):
            raise PyMongoError("Database connection failed")

        service = ConversationService()
        db_error_method(service, self.mock_request, self.mock_context)
        
        # Verify context.abort was called with INTERNAL status
        self.mock_context.abort.assert_called_once_with(
            grpc.StatusCode.INTERNAL, 
            "Database error: Database connection failed"
        )

    def test_timeout_handler_general_error(self):
        """Test timeout handler with general error."""
        @timeout_handler(timeout_seconds=5)
        def error_method(self, request, context):
            raise ValueError("Invalid input")

        service = ConversationService()
        error_method(service, self.mock_request, self.mock_context)
        
        # Verify context.abort was called with INTERNAL status
        self.mock_context.abort.assert_called_once_with(
            grpc.StatusCode.INTERNAL, 
            "Internal error: Invalid input"
        )


class TestBatchLoadTasks(unittest.TestCase):
    """Test cases for the batch_load_tasks function."""

    @patch('app.services.conversation_service.Task')
    def test_batch_load_tasks_success(self, mock_task):
        """Test successful batch loading of tasks."""
        # Mock task objects
        task1 = Mock()
        task1.globalChatConversationId = "conv1"
        task2 = Mock()
        task2.globalChatConversationId = "conv1"
        task3 = Mock()
        task3.globalChatConversationId = "conv2"
        
        mock_task.objects.filter.return_value = [task1, task2, task3]
        
        conversation_ids = ["conv1", "conv2"]
        result = batch_load_tasks(conversation_ids)
        
        # Verify the query was made correctly
        mock_task.objects.filter.assert_called_once_with(
            globalChatConversationId__in=conversation_ids
        )
        
        # Verify the grouping is correct
        expected = {
            "conv1": [task1, task2],
            "conv2": [task3]
        }
        self.assertEqual(result, expected)

    def test_batch_load_tasks_empty_list(self):
        """Test batch loading with empty conversation list."""
        result = batch_load_tasks([])
        self.assertEqual(result, {})

    @patch('app.services.conversation_service.Task')
    def test_batch_load_tasks_error(self, mock_task):
        """Test batch loading with database error."""
        mock_task.objects.filter.side_effect = Exception("Database error")
        
        conversation_ids = ["conv1", "conv2"]
        result = batch_load_tasks(conversation_ids)
        
        # Should return empty dict on error
        self.assertEqual(result, {})


class TestConversationService(unittest.TestCase):
    """Test cases for the ConversationService class."""

    def setUp(self):
        """Set up test fixtures."""
        self.service = ConversationService()
        self.mock_context = Mock(spec=grpc.ServicerContext)

    @patch('app.services.conversation_service.Conversation')
    @patch('app.services.conversation_service.Task')
    def test_create_conversation_new(self, mock_task, mock_conversation):
        """Test creating a new conversation."""
        # Mock request
        request = Mock()
        request.userId = "user123"
        request.agentId = "agent456"
        request.chatType = 1  # CHAT_TYPE_GLOBAL
        
        # Mock no existing conversations
        mock_conversation.objects.filter.return_value.order_by.return_value.first.return_value = None
        
        # Mock new conversation creation
        new_conversation = Mock()
        new_conversation.id = "new_conv_id"
        new_conversation.to_proto.return_value = "proto_conversation"
        mock_conversation.return_value = new_conversation
        
        # Mock tasks query
        mock_task.objects.filter.return_value = []
        
        # Mock ChatType.Name
        with patch('app.services.conversation_service.communication_pb2.ChatType.Name') as mock_name:
            mock_name.return_value = "CHAT_TYPE_GLOBAL"
            
            result = self.service.createConversation(request, self.mock_context)
            
            # Verify new conversation was created
            mock_conversation.assert_called_once()
            new_conversation.save.assert_called_once()
            self.assertEqual(result, "proto_conversation")

    @patch('app.services.conversation_service.Conversation')
    @patch('app.services.conversation_service.Message')
    @patch('app.services.conversation_service.Task')
    def test_create_conversation_return_existing_global(self, mock_task, mock_message, mock_conversation):
        """Test returning existing global conversation with no messages and tasks."""
        # Mock request
        request = Mock()
        request.userId = "user123"
        request.agentId = "agent456"
        request.chatType = 1  # CHAT_TYPE_GLOBAL
        
        # Mock existing conversation
        existing_conversation = Mock()
        existing_conversation.id = "existing_conv_id"
        existing_conversation.to_proto.return_value = "existing_proto_conversation"
        mock_conversation.objects.filter.return_value.order_by.return_value.first.return_value = existing_conversation
        
        # Mock no messages and no tasks
        mock_message.objects.filter.return_value.count.return_value = 0
        mock_task_filter = Mock()
        mock_task_filter.count.return_value = 0
        mock_task.objects.filter.return_value = mock_task_filter
        
        # Mock ChatType.Name
        with patch('app.services.conversation_service.communication_pb2.ChatType.Name') as mock_name:
            mock_name.return_value = "CHAT_TYPE_GLOBAL"
            
            result = self.service.createConversation(request, self.mock_context)
            
            # Verify existing conversation was returned
            self.assertEqual(result, "existing_proto_conversation")

    @patch('app.services.conversation_service.Conversation')
    def test_get_conversation_success(self, mock_conversation):
        """Test successful conversation retrieval."""
        # Mock request
        request = Mock()
        request.conversationId = "conv123"
        request.userId = "user123"
        
        # Mock conversation
        conversation = Mock()
        conversation.userId = "user123"
        conversation.to_proto.return_value = "proto_conversation"
        mock_conversation.objects.get.return_value = conversation
        
        # Mock tasks
        with patch('app.services.conversation_service.Task') as mock_task:
            mock_task.objects.filter.return_value = []
            
            result = self.service.getConversation(request, self.mock_context)
            
            # Verify conversation was retrieved
            mock_conversation.objects.get.assert_called_once_with(id="conv123")
            self.assertEqual(result, "proto_conversation")

    @patch('app.services.conversation_service.Conversation')
    def test_get_conversation_unauthorized(self, mock_conversation):
        """Test unauthorized conversation access."""
        # Mock request
        request = Mock()
        request.conversationId = "conv123"
        request.userId = "user123"

        # Mock conversation with different user
        conversation = Mock()
        conversation.userId = "different_user"
        conversation.id = "conv123"
        mock_conversation.objects.get.return_value = conversation

        # Mock the context to prevent database connection errors
        self.mock_context.abort.side_effect = Exception("Aborted")
        
        try:
            self.service.getConversation(request, self.mock_context)
        except Exception:
            pass  # Expected due to abort

        # Verify permission denied error was called
        abort_calls = self.mock_context.abort.call_args_list
        permission_denied_called = any(
            call[0][0] == grpc.StatusCode.PERMISSION_DENIED
            for call in abort_calls
        )
        self.assertTrue(permission_denied_called)

    @patch('app.services.conversation_service.Conversation')
    def test_update_conversation_tokens_success(self, mock_conversation):
        """Test successful token update."""
        # Mock request
        request = Mock()
        request.conversationId = "conv123"
        request.userId = "user123"
        request.inputTokens = 100
        request.outputTokens = 50
        
        # Mock conversation
        conversation = Mock()
        conversation.userId = "user123"
        conversation.inputTokens = 200
        conversation.outputTokens = 150
        mock_conversation.objects.get.return_value = conversation
        
        result = self.service.updateConversationTokens(request, self.mock_context)
        
        # Verify tokens were updated
        self.assertEqual(conversation.inputTokens, 300)  # 200 + 100
        self.assertEqual(conversation.outputTokens, 200)  # 150 + 50
        conversation.save.assert_called_once()
        self.assertIsInstance(result, empty_pb2.Empty)

    @patch('app.services.conversation_service.Conversation')
    def test_delete_conversation_success(self, mock_conversation):
        """Test successful conversation deletion."""
        # Mock request
        request = Mock()
        request.conversationId = "conv123"
        request.userId = "user123"
        
        # Mock conversation
        conversation = Mock()
        conversation.userId = "user123"
        mock_conversation.objects.get.return_value = conversation
        
        result = self.service.deleteConversation(request, self.mock_context)
        
        # Verify conversation was deleted
        conversation.delete.assert_called_once()
        self.assertIsInstance(result, empty_pb2.Empty)

    @patch('app.services.conversation_service.Conversation')
    @patch('app.services.conversation_service.batch_load_tasks')
    def test_list_conversations_with_batch_loading(self, mock_batch_load, mock_conversation):
        """Test conversation listing with batch loading optimization."""
        # Mock request with proper attributes
        request = Mock()
        request.userId = "user123"
        request.chatType = 0  # No filter
        request.agentId = ""
        request.limit = 10
        request.page = 1
        request.search = ""  # Add missing search attribute
        
        # Mock conversations with GLOBAL chatType to trigger batch loading
        conv1 = Mock()
        conv1.id = "conv1"
        conv1.chatType = "CHAT_TYPE_GLOBAL"  # This will trigger batch loading
        conv1.to_proto.return_value = "proto_conv1"
        conv2 = Mock()
        conv2.id = "conv2"
        conv2.chatType = "CHAT_TYPE_GLOBAL"  # This will trigger batch loading
        conv2.to_proto.return_value = "proto_conv2"

        conversations = [conv1, conv2]
        
        # Mock query chain with proper slicing support and only() method
        mock_filter = Mock()
        mock_filter.count.return_value = 2
        mock_filter.__getitem__ = Mock(return_value=conversations)
        mock_only = Mock()
        mock_only.order_by.return_value = mock_filter
        mock_conversation.objects.return_value.only.return_value = mock_only
        
        # Mock batch loading
        mock_batch_load.return_value = {
            "conv1": ["task1"],
            "conv2": ["task2"]
        }
        
        # Mock Q import
        with patch('mongoengine.Q') as mock_q:
            mock_q.return_value = Mock()
            
            result = self.service.listConversations(request, self.mock_context)
            
            # Verify batch loading was called
            mock_batch_load.assert_called_once_with(["conv1", "conv2"])
            
            # Verify conversations were converted to proto with tasks
            conv1.to_proto.assert_called_once_with(tasks=["task1"])
            conv2.to_proto.assert_called_once_with(tasks=["task2"])


class TestPerformanceOptimizations(unittest.TestCase):
    """Test cases for performance optimizations."""

    def test_timeout_decorator_applied(self):
        """Test that timeout decorators are applied to service methods."""
        service = ConversationService()
        
        # Check that methods have the timeout decorator
        methods_with_timeout = [
            'createConversation',
            'getConversation', 
            'updateConversationTokens',
            'deleteConversation',
            'listConversations'
        ]
        
        for method_name in methods_with_timeout:
            method = getattr(service, method_name)
            # The decorator should modify the function, so we check it exists
            self.assertTrue(callable(method))

    @patch('app.services.conversation_service.Task')
    def test_batch_loading_efficiency(self, mock_task):
        """Test that batch loading reduces database queries."""
        # Mock multiple tasks for different conversations
        tasks = []
        for i in range(10):
            task = Mock()
            task.globalChatConversationId = f"conv{i % 3}"  # 3 conversations
            tasks.append(task)
        
        mock_task.objects.filter.return_value = tasks
        
        conversation_ids = ["conv0", "conv1", "conv2"]
        result = batch_load_tasks(conversation_ids)
        
        # Verify only one database query was made
        mock_task.objects.filter.assert_called_once()
        
        # Verify tasks are properly grouped
        self.assertEqual(len(result), 3)
        for conv_id in conversation_ids:
            self.assertIn(conv_id, result)


if __name__ == '__main__':
    unittest.main()