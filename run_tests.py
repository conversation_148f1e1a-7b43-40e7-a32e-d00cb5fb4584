#!/usr/bin/env python3
"""
Test runner script for the communication service.

This script provides various options for running tests including unit tests,
integration tests, performance tests, and coverage reporting.
"""

import sys
import subprocess
import argparse
import os


def run_command(command, description):
    """
    Run a command and handle errors.

    Args:
        command: Command to run as a list
        description: Description of what the command does
    """
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print(f"{'='*60}")

    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {description} failed!")
        print(f"Return code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False


def install_dependencies():
    """Install test dependencies."""
    return run_command(
        ["poetry", "install", "--with", "test"], "Installing test dependencies with Poetry"
    )


def run_unit_tests(coverage=False, verbose=False):
    """Run unit tests."""
    command = [sys.executable, "-m", "pytest", "tests/", "-m", "unit"]

    if coverage:
        command.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])

    if verbose:
        command.append("-v")

    return run_command(command, "Running unit tests")


def run_integration_tests(verbose=False):
    """Run integration tests."""
    command = [sys.executable, "-m", "pytest", "tests/", "-m", "integration"]

    if verbose:
        command.append("-v")

    return run_command(command, "Running integration tests")


def run_performance_tests(verbose=False):
    """Run performance tests."""
    command = [sys.executable, "-m", "pytest", "tests/", "-m", "performance"]

    if verbose:
        command.append("-v")

    return run_command(command, "Running performance tests")


def run_all_tests(coverage=False, verbose=False):
    """Run all tests."""
    command = [sys.executable, "-m", "pytest", "tests/"]

    if coverage:
        command.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])

    if verbose:
        command.append("-v")

    return run_command(command, "Running all tests")


def run_specific_test(test_path, verbose=False):
    """Run a specific test file or test function."""
    command = [sys.executable, "-m", "pytest", test_path]

    if verbose:
        command.append("-v")

    return run_command(command, f"Running specific test: {test_path}")


def run_code_quality_checks():
    """Run code quality checks."""
    checks = [
        ([sys.executable, "-m", "flake8", "app/", "tests/"], "Running flake8 linting"),
        (
            [sys.executable, "-m", "black", "--check", "app/", "tests/"],
            "Running black formatting check",
        ),
        (
            [sys.executable, "-m", "isort", "--check-only", "app/", "tests/"],
            "Running isort import sorting check",
        ),
    ]

    all_passed = True
    for command, description in checks:
        if not run_command(command, description):
            all_passed = False

    return all_passed


def generate_coverage_report():
    """Generate detailed coverage report."""
    commands = [
        (
            [
                sys.executable,
                "-m",
                "pytest",
                "tests/",
                "--cov=app",
                "--cov-report=html",
                "--cov-report=term",
            ],
            "Generating coverage report",
        ),
        (["open", "htmlcov/index.html"], "Opening coverage report in browser (macOS)"),
    ]

    for command, description in commands:
        if not run_command(command, description):
            if "open" in command:
                print(
                    "Note: Could not open browser automatically. Check htmlcov/index.html manually."
                )
            else:
                return False

    return True


def main():
    """Main function to handle command line arguments and run tests."""
    parser = argparse.ArgumentParser(description="Test runner for communication service")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--performance", action="store_true", help="Run performance tests only")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("--quality", action="store_true", help="Run code quality checks")
    parser.add_argument("--test", type=str, help="Run specific test file or function")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")

    args = parser.parse_args()

    # If no specific action is specified, run all tests
    if not any(
        [
            args.install_deps,
            args.unit,
            args.integration,
            args.performance,
            args.all,
            args.coverage,
            args.quality,
            args.test,
        ]
    ):
        args.all = True

    success = True

    # Install dependencies if requested
    if args.install_deps:
        if not install_dependencies():
            success = False

    # Run code quality checks
    if args.quality:
        if not run_code_quality_checks():
            success = False

    # Run specific test
    if args.test:
        if not run_specific_test(args.test, args.verbose):
            success = False

    # Run unit tests
    if args.unit:
        if not run_unit_tests(args.coverage, args.verbose):
            success = False

    # Run integration tests
    if args.integration:
        if not run_integration_tests(args.verbose):
            success = False

    # Run performance tests
    if args.performance:
        if not run_performance_tests(args.verbose):
            success = False

    # Run all tests
    if args.all:
        if not run_all_tests(args.coverage, args.verbose):
            success = False

    # Generate coverage report
    if args.coverage and not (args.unit or args.all):
        if not generate_coverage_report():
            success = False

    # Print summary
    print(f"\n{'='*60}")
    if success:
        print("✅ All operations completed successfully!")
    else:
        print("❌ Some operations failed. Check the output above.")
    print(f"{'='*60}")

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
