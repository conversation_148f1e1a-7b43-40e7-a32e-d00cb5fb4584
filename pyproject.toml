[tool.poetry]
name = "communication-service"
version = "0.1.0"
description = "Communication Microservice"
authors = ["Your Name <<EMAIL>>"]
packages = [
    { include = "app", from = "." },
]

[tool.poetry.dependencies]
python = "^3.11"
grpcio = "1.71.0"
structlog = "25.2.0"
python-dotenv = "1.0.1"
grpcio-health-checking = "1.71.0"
grpcio-tools = "1.71.0"
mongoengine = "0.29.1"
pymongo = "4.11.2"
google-generativeai = "0.8.4"
pydantic = "^2.5.3"
pydantic-settings = "^2.1.0"

[tool.poetry.group.dev.dependencies]
black = "25.1.0"
isort = "6.0.1"
mypy = "1.15.0"
pylint = "3.3.5"
ruff = "0.9.10"
colorlog = "6.9.0"

[tool.poetry.group.test.dependencies]
pytest = "^7.0.0"
pytest-cov = "^4.0.0"
pytest-mock = "^3.10.0"
pytest-asyncio = "^0.21.0"
coverage = "^7.0.0"
grpcio-testing = "^1.50.0"
factory-boy = "^3.2.0"
faker = "^18.0.0"
freezegun = "^1.2.0"
responses = "^0.23.0"
pytest-benchmark = "^4.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ["py311"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pylint.messages_control]
disable = ["C0111", "C0103", "C0330", "C0326"]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
markers = [
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
    "performance: marks tests as performance tests",
]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"
